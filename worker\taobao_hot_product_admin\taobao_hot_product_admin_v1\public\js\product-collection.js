$(document).ready(function() {
    let collectionInterval = null;
    let isCollecting = false;
    let isPaused = false; // 暂停状态标志
    let isRequesting = false; // 添加请求状态标志
    let currentPageNum = 1;
    let collectedProducts = [];
    let selectedAnchor = null;
    let conditionData = null;
    let shopKeywords = []; // 店铺关键词列表
    let currentShopIndex = 0; // 当前采集的店铺索引

    // 采集进度保存
    let collectionProgress = {
        filters: null,
        pageSize: 30,
        interval: 3000,
        currentPageNum: 1,
        currentShopIndex: 0,
        shopKeywords: [],
        selectedAnchor: null,
        selectedTag: null,
        collectedProducts: []
    };

    // 初始化页面
    init();

    async function init() {
        try {
            // 加载筛选条件
            await loadConditions();
            // 加载主播列表
            await loadAnchors();
            // 绑定事件
            bindEvents();
            // 检查是否有保存的采集进度
            checkSavedProgress();
        } catch (error) {
            console.error('初始化失败:', error);
            addLog('初始化失败: ' + error.message, 'error');
        }
    }

    // 检查保存的采集进度
    function checkSavedProgress() {
        if (collectionProgress.filters && collectionProgress.selectedAnchor) {
            isPaused = true;
            selectedAnchor = collectionProgress.selectedAnchor;
            currentPageNum = collectionProgress.currentPageNum;
            currentShopIndex = collectionProgress.currentShopIndex;
            shopKeywords = collectionProgress.shopKeywords;
            collectedProducts = collectionProgress.collectedProducts;

            // 恢复UI选择状态
            restoreAnchorSelection();
            restoreTagSelection();
            restoreShopKeywords();
            restoreFilterSelections();

            // 更新UI状态
            updateCollectionStatus('已暂停', false, true);
            document.getElementById('startCollectionBtn').innerHTML = '<i class="fas fa-play"></i>继续采集';

            // 更新统计信息
            document.getElementById('currentPage').textContent = currentPageNum;
            document.getElementById('collectedCount').textContent = collectedProducts.length;

            // 恢复表格数据
            if (collectedProducts.length > 0) {
                updateProductTable(collectedProducts, false);
            }

            addLog('检测到未完成的采集任务，可点击继续采集按钮恢复', 'info');
        }
    }

    // 加载筛选条件
    async function loadConditions() {
        try {
            // 基于condition.json实际结构的筛选条件数据
            conditionData = {
                "cateIds": [
                    {"name": "美容美妆", "value": "50010788,126762001", "code": "1"},
                    {"name": "个护清洁", "value": "50023282,1801,50016348,50023722,50025705", "code": "2"},
                    {"name": "母婴用品", "value": "50022517,50014812,201207402,35,50018004", "code": "3"},
                    {"name": "童装童鞋", "value": "50008165,122650005", "code": "4"},
                    {"name": "服饰", "value": "16,1625,50013864,50006843,30,50010404,50011740", "code": "5"},
                    {"name": "手表眼镜", "value": "50468001,28", "code": "6"},
                    {"name": "箱包皮具", "value": "50006842", "code": "7"},
                    {"name": "珠宝", "value": "50011397", "code": "8"},
                    {"name": "生鲜", "value": "50050359", "code": "9"},
                    {"name": "粮油副食", "value": "50016422", "code": "10"},
                    {"name": "零食饮品", "value": "124458005,50026316,50008141,50002766", "code": "11"},
                    {"name": "营养保健", "value": "50026800,50020275", "code": "12"},
                    {"name": "3C数码", "value": "50008090,50012164,11,124242008,50017300,50007218,20", "code": "13"},
                    {"name": "大家电", "value": "50022703", "code": "14"},
                    {"name": "生活电器", "value": "50012100,50002768,50012082,50011972", "code": "15"},
                    {"name": "家居百货", "value": "122852001,21,50008163,122928002,50020808,50025004,50020857,122950001", "code": "16"},
                    {"name": "厨房用具", "value": "122952001,50016349", "code": "17"},
                    {"name": "家装", "value": "50008164,27,126700003,50020332,50020485,50023804,50020611,50020579,124050001", "code": "18"},
                    {"name": "运动服鞋", "value": "50011699,50012029", "code": "19"},
                    {"name": "户外装备", "value": "50013886,122684003,50010728,50510002", "code": "20"},
                    {"name": "汽车", "value": "201162107,26,124354002,50074001", "code": "21"},
                    {"name": "玩具", "value": "25,124484008", "code": "22"},
                    {"name": "鲜花园艺", "value": "50007216", "code": "23"},
                    {"name": "宠物", "value": "29", "code": "24"},
                    {"name": "本地生活", "value": "50025111,50008075,50025110,201173506", "code": "25"},
                    {"name": "图画音像", "value": "33,34,23", "code": "26"},
                    {"name": "教育培训", "value": "50014927", "code": "27"},
                    {"name": "医疗健康", "value": "50023717,50023721", "code": "28"}
                ],
                "featured": [
                    {"name": "爆品", "value": "featuredPool=4;10796,featuredPool=4;10795,featuredPool=4;10794"},
                    {"name": "高佣", "value": "featured=30日高佣好货,featured=7日高佣好货,featured=90日高佣好货"},
                    {"name": "秒杀引流", "value": "featured=秒杀品"},
                    {"name": "稀缺", "value": "featured=稀缺品牌"},
                    {"name": "热销", "value": "featured=销量top单品,featured=成交top单品"},
                    {"name": "超级好价", "value": "starLevel=4,starLevel=5,compareResult=-1,realTimeCompareResult=-1"},
                    {"name": "新品", "value": "newItemTag=重磅新品,newItemTag=优质新品,newItemTag=天猫新品,newItemTag=淘宝新品"},
                    {"name": "高曝品", "value": "isHotItem=是"}
                ],
                "priceSelect": [
                    {"name": "1-100元", "value": "1_100"},
                    {"name": "100-300元", "value": "100_300"},
                    {"name": "300-600元", "value": "300_600"},
                    {"name": "600-1200元", "value": "600_1200"},
                    {"name": "1200-2000元", "value": "1200_2000"},
                    {"name": "2000元以上", "value": "2000_"}
                ],
                "commissionRateSelect": [
                    {"name": "1-5%", "value": "1_5"},
                    {"name": "5-10%", "value": "5_10"},
                    {"name": "10-20%", "value": "10_20"},
                    {"name": "20%以上", "value": "20_"}
                ],
                "soldQuantity365Select": [
                    {"name": "10-100件", "value": "10_100"},
                    {"name": "100-500件", "value": "100_500"},
                    {"name": "500-1000件", "value": "500_1000"},
                    {"name": "1000-5000件", "value": "1000_5000"},
                    {"name": "5000件以上", "value": "5000_"}
                ],
                "shopType": [
                    {"name": "天猫店", "value": "天猫店"},
                    {"name": "淘宝店", "value": "淘宝店"}
                ],
                "shopLevel": [
                    {"name": "5星级", "value": "5星级"},
                    {"name": "4星级", "value": "4星级"},
                    {"name": "3星级", "value": "3星级"},
                    {"name": "2星级", "value": "2星级"},
                    {"name": "1星级", "value": "1星级"},
                    {"name": "金冠级", "value": "1红冠,2红冠,3红冠,4红冠,5红冠"},
                    {"name": "皇冠级", "value": "1皇冠,2皇冠,3皇冠,4皇冠,5皇冠"},
                    {"name": "钻级", "value": "1钻,2钻,3钻,4钻,5钻"},
                    {"name": "心级", "value": "1心,2心,3心,4心,5心"}
                ],
                "icTags": [
                    {"name": "直播严选", "value": "2156610,2205634"},
                    {"name": "天猫超市", "value": "2388418"},
                    {"name": "源头优选", "value": "2243138"},
                    {"name": "淘工厂", "value": "1362498"},
                    {"name": "天天热卖", "value": "2758466,3554370"},
                    {"name": "天猫U先", "value": "271938"},
                    {"name": "淘宝秒杀", "value": "1792066"},
                    {"name": "淘宝买菜", "value": "3117442"}
                ]
            };
            renderConditions();
        } catch (error) {
            console.error('加载筛选条件失败:', error);
            addLog('加载筛选条件失败: ' + error.message, 'error');
        }
    }

    // 加载主播列表
    async function loadAnchors() {
        try {
            const response = await fetch('/api/product-collection/anchors', {
                headers: {
                    'X-API-Key': localStorage.getItem('apiKey') || ''
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (result.success) {
                renderAnchors(result.data);
            } else {
                throw new Error(result.error || '获取主播列表失败');
            }
        } catch (error) {
            console.error('加载主播列表失败:', error);
            addLog('加载主播列表失败: ' + error.message, 'error');
        }
    }

    // 渲染筛选条件
    function renderConditions() {
        if (!conditionData) return;

        // 渲染商品类目
        if (conditionData.cateIds) {
            renderFilterButtons('categoryFilters', conditionData.cateIds, 'cateIds');
        }

        // 渲染商品标签
        if (conditionData.featured) {
            renderFilterButtons('featuredFilters', conditionData.featured, 'featured');
        }

        // 商品价格、佣金比例、365天销量现在使用输入框，不需要渲染按钮

        // 渲染店铺类型
        if (conditionData.shopType) {
            renderFilterButtons('shopTypeFilters', conditionData.shopType, 'shopType');
        }

        // 渲染店铺等级
        if (conditionData.shopLevel) {
            renderFilterButtons('shopLevelFilters', conditionData.shopLevel, 'shopLevel');
        }

        // 渲染商品渠道
        if (conditionData.icTags) {
            renderFilterButtons('icTagsFilters', conditionData.icTags, 'icTags');
        }
    }

    // 渲染筛选按钮
    function renderFilterButtons(containerId, options, filterType) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (Array.isArray(options)) {
            options.forEach(option => {
                const button = createFilterButton(option.name || option.label || option, option.value || option, filterType, option.code);
                container.appendChild(button);
            });
        } else if (typeof options === 'object') {
            Object.entries(options).forEach(([key, value]) => {
                const button = createFilterButton(value.name || value.label || key, value.value || key, filterType, value.code);
                container.appendChild(button);
            });
        }
    }

    // 创建筛选按钮
    function createFilterButton(text, value, filterType, code) {
        const button = document.createElement('button');
        button.className = 'filter-button';
        button.textContent = text;
        button.dataset.value = value;
        button.dataset.filterType = filterType;
        if (code) {
            button.dataset.code = code;
        }

        button.addEventListener('click', function() {
            // 对于某些筛选类型，只允许单选
            if (['priceSelect', 'commissionRateSelect', 'soldQuantity365Select'].includes(filterType)) {
                // 清除同类型的其他选中状态
                const siblings = this.parentElement.querySelectorAll('.filter-button');
                siblings.forEach(sibling => sibling.classList.remove('active'));
            }

            this.classList.toggle('active');
        });

        return button;
    }

    // 渲染主播列表
    function renderAnchors(anchors) {
        const container = document.getElementById('anchorButtons');
        container.innerHTML = '';

        anchors.forEach(anchor => {
            const button = document.createElement('button');
            button.className = 'anchor-button';
            button.textContent = anchor.anchor_name;
            button.dataset.anchorId = anchor.anchor_id;

            button.addEventListener('click', function() {
                // 清除其他主播的选中状态
                const siblings = this.parentElement.querySelectorAll('.anchor-button');
                siblings.forEach(sibling => sibling.classList.remove('active'));

                // 设置当前主播为选中状态
                this.classList.add('active');
                selectedAnchor = anchor;
                addLog(`已选择主播: ${anchor.anchor_name}`, 'info');
            });

            container.appendChild(button);
        });
    }

    // 绑定事件
    function bindEvents() {
        // 开始采集按钮
        document.getElementById('startCollectionBtn').addEventListener('click', startOrResumeCollection);

        // 暂停采集按钮
        document.getElementById('pauseCollectionBtn').addEventListener('click', pauseCollection);

        // 停止采集按钮
        document.getElementById('stopCollectionBtn').addEventListener('click', stopCollection);

        // 清空日志按钮
        document.getElementById('clearLogBtn').addEventListener('click', clearLog);

        // 条件数据显示/隐藏按钮
        document.getElementById('toggleConditionBtn').addEventListener('click', toggleConditionData);

        // Tag按钮事件
        document.querySelectorAll('.tag-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除其他按钮的active状态
                document.querySelectorAll('.tag-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active状态
                this.classList.add('active');
            });
        });
    }

    // 开始或继续采集
    async function startOrResumeCollection() {
        if (isCollecting) {
            console.log('采集已在进行中，忽略重复请求');
            return;
        }

        // 检查是否有保存的进度
        if (isPaused && collectionProgress.filters) {
            // 继续采集
            await resumeCollection();
        } else {
            // 开始新的采集
            await startNewCollection();
        }
    }

    // 开始新的采集
    async function startNewCollection() {
        if (!selectedAnchor) {
            layer.msg('请先选择主播', {icon: 2});
            return;
        }

        // 检查是否选择了tag
        const selectedTag = getSelectedTag();
        if (!selectedTag) {
            layer.msg('请选择商品标签', {icon: 2});
            return;
        }

        const filters = getSelectedFilters();
        const interval = parseInt(document.getElementById('intervalInput').value) * 1000;
        const pageSize = parseInt(document.getElementById('pageSizeSelect').value);

        if (interval < 1000) {
            layer.msg('请求间隔不能少于1秒', {icon: 2});
            return;
        }

        // 获取店铺关键词列表
        const shopKeywordsText = document.getElementById('shopKeywords').value.trim();
        shopKeywords = shopKeywordsText ? shopKeywordsText.split('\n').map(k => k.trim()).filter(k => k) : [];

        // 保存采集进度
        saveCollectionProgress(filters, pageSize, interval, selectedTag);

        // 重置状态
        isCollecting = true;
        isPaused = false;
        currentPageNum = 1;
        currentShopIndex = 0;
        collectedProducts = [];

        // 清空表格
        const tbody = document.getElementById('productTableBody');
        tbody.innerHTML = '';

        updateCollectionStatus('采集中...', true, false);
        addLog('开始采集商品数据...', 'info');
        addLog(`主播: ${selectedAnchor.anchor_name}`, 'info');
        addLog(`筛选条件: ${JSON.stringify(filters)}`, 'info');
        addLog(`请求间隔: ${interval/1000}秒`, 'info');

        if (shopKeywords.length > 0) {
            addLog(`店铺列表: ${shopKeywords.length} 个店铺`, 'info');
            shopKeywords.forEach((keyword, index) => {
                addLog(`  ${index + 1}. ${keyword}`, 'info');
            });
        } else {
            addLog('未设置店铺筛选，采集所有店铺', 'info');
        }

        // 显示条件数据
        displayConditionData(filters);

        // 开始采集流程
        await startCollectionProcess(filters, pageSize, interval);
    }

    // 继续采集
    async function resumeCollection() {
        if (!collectionProgress.filters) {
            addLog('没有保存的采集进度，请重新开始采集', 'error');
            return;
        }

        // 恢复采集状态
        isCollecting = true;
        isPaused = false;
        currentPageNum = collectionProgress.currentPageNum;
        currentShopIndex = collectionProgress.currentShopIndex;
        shopKeywords = collectionProgress.shopKeywords;
        selectedAnchor = collectionProgress.selectedAnchor;
        collectedProducts = collectionProgress.collectedProducts;

        // 恢复主播选择状态
        restoreAnchorSelection();

        // 恢复商品标签选择状态
        restoreTagSelection();

        // 恢复店铺关键词
        restoreShopKeywords();

        // 恢复筛选条件
        restoreFilterSelections();

        updateCollectionStatus('采集中...', true, false);
        addLog('继续采集商品数据...', 'info');
        addLog(`从第 ${currentPageNum} 页继续采集`, 'info');

        if (shopKeywords.length > 0) {
            addLog(`从店铺 ${shopKeywords[currentShopIndex]} (${currentShopIndex + 1}/${shopKeywords.length}) 继续采集`, 'info');
        }

        // 更新按钮文本
        document.getElementById('startCollectionBtn').innerHTML = '<i class="fas fa-play"></i>开始采集';

        // 继续采集流程
        await startCollectionProcess(
            collectionProgress.filters,
            collectionProgress.pageSize,
            collectionProgress.interval
        );
    }

    // 暂停采集
    function pauseCollection() {
        if (!isCollecting) {
            return;
        }

        isPaused = true;
        isCollecting = false;

        // 清除定时器
        if (collectionInterval) {
            clearInterval(collectionInterval);
            collectionInterval = null;
        }

        // 清理店铺采集的resolve函数
        if (window.currentShopResolve) {
            window.currentShopResolve();
            window.currentShopResolve = null;
        }

        // 保存当前进度
        updateCollectionProgress();

        updateCollectionStatus('已暂停', false, true);
        addLog('采集已暂停，可点击继续采集按钮恢复', 'info');

        // 更新按钮文本
        document.getElementById('startCollectionBtn').innerHTML = '<i class="fas fa-play"></i>继续采集';
    }

    // 保存采集进度
    function saveCollectionProgress(filters, pageSize, interval, selectedTag) {
        collectionProgress = {
            filters: filters,
            pageSize: pageSize,
            interval: interval,
            currentPageNum: currentPageNum,
            currentShopIndex: currentShopIndex,
            shopKeywords: [...shopKeywords],
            selectedAnchor: selectedAnchor,
            selectedTag: selectedTag,
            collectedProducts: [...collectedProducts]
        };
    }

    // 更新采集进度
    function updateCollectionProgress() {
        if (collectionProgress.filters) {
            collectionProgress.currentPageNum = currentPageNum;
            collectionProgress.currentShopIndex = currentShopIndex;
            collectionProgress.collectedProducts = [...collectedProducts];
        }
    }

    // 恢复主播选择状态
    function restoreAnchorSelection() {
        if (selectedAnchor) {
            // 清除所有主播按钮的选中状态
            document.querySelectorAll('.anchor-button').forEach(btn => btn.classList.remove('active'));

            // 找到对应的主播按钮并设置为选中状态
            const anchorButton = document.querySelector(`[data-anchor-id="${selectedAnchor.anchor_id}"]`);
            if (anchorButton) {
                anchorButton.classList.add('active');
            }
        }
    }

    // 恢复商品标签选择状态
    function restoreTagSelection() {
        if (collectionProgress.selectedTag) {
            // 清除所有标签按钮的选中状态
            document.querySelectorAll('.tag-btn').forEach(btn => btn.classList.remove('active'));

            // 找到对应的标签按钮并设置为选中状态
            const tagButton = document.querySelector(`[data-tag="${collectionProgress.selectedTag}"]`);
            if (tagButton) {
                tagButton.classList.add('active');
            }
        }
    }

    // 恢复店铺关键词
    function restoreShopKeywords() {
        if (collectionProgress.shopKeywords && collectionProgress.shopKeywords.length > 0) {
            const shopKeywordsTextarea = document.getElementById('shopKeywords');
            if (shopKeywordsTextarea) {
                shopKeywordsTextarea.value = collectionProgress.shopKeywords.join('\n');
            }
        }
    }

    // 恢复筛选条件选择状态
    function restoreFilterSelections() {
        if (!collectionProgress.filters) return;

        const filters = collectionProgress.filters;

        // 恢复价格范围
        if (filters.priceSelect) {
            const [min, max] = filters.priceSelect.split('_');
            if (min !== '0') document.getElementById('priceMin').value = min;
            if (max !== '999999') document.getElementById('priceMax').value = max;
        }

        // 恢复佣金比例范围
        if (filters.commissionRateSelect) {
            const [min, max] = filters.commissionRateSelect.split('_');
            if (min !== '0') document.getElementById('commissionMin').value = min;
            if (max !== '100') document.getElementById('commissionMax').value = max;
        }

        // 恢复销量范围
        if (filters.soldQuantity365Select) {
            const [min, max] = filters.soldQuantity365Select.split('_');
            if (min !== '0') document.getElementById('salesMin').value = min;
            if (max !== '999999') document.getElementById('salesMax').value = max;
        }

        // 恢复佣金金额范围
        if (filters.commissionRange) {
            if (filters.commissionRange.min > 0) {
                document.getElementById('commissionRangeMin').value = filters.commissionRange.min;
            }
            if (filters.commissionRange.max < 999999) {
                document.getElementById('commissionRangeMax').value = filters.commissionRange.max;
            }
        }
    }

    // 采集流程控制
    async function startCollectionProcess(filters, pageSize, interval) {
        const wasResuming = isPaused; // 记录是否是从暂停状态恢复
        isPaused = false; // 开始采集时重置暂停状态

        if (shopKeywords.length === 0) {
            // 没有店铺列表，按原逻辑采集
            startContinuousCollection(filters, pageSize, interval);
        } else {
            // 有店铺列表，循环每个店铺
            for (; currentShopIndex < shopKeywords.length; currentShopIndex++) {
                if (!isCollecting) break;

                const keyword = shopKeywords[currentShopIndex];

                // 如果是从暂停状态恢复，显示继续采集信息
                if (wasResuming && currentShopIndex === collectionProgress.currentShopIndex) {
                    addLog(`继续采集店铺: ${keyword} (${currentShopIndex + 1}/${shopKeywords.length})`, 'info');
                    wasResuming = false; // 只在第一个店铺显示继续信息
                } else {
                    addLog(`开始采集店铺: ${keyword} (${currentShopIndex + 1}/${shopKeywords.length})`, 'info');
                }

                // 为当前店铺添加keyword参数
                const shopFilters = { ...filters, keyword: keyword };
                console.log(`店铺筛选条件:`, shopFilters);

                // 如果不是从暂停状态恢复或者已经切换到新店铺，重置页码
                if (currentShopIndex !== collectionProgress.currentShopIndex) {
                    currentPageNum = 1;
                }

                // 采集当前店铺的所有页面（等待完成）
                await collectAllPagesForShop(shopFilters, pageSize, interval);

                if (currentShopIndex < shopKeywords.length - 1 && isCollecting) {
                    addLog(`店铺 ${keyword} 采集完成，等待 ${interval/1000} 秒后采集下一个店铺...`, 'info');
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }

            if (isCollecting) {
                addLog(`所有店铺采集完成，共采集 ${shopKeywords.length} 个店铺`, 'info');
                stopCollection();
            }
        }
    }

    // 为单个店铺采集所有页面（等待完成）
    async function collectAllPagesForShop(filters, pageSize, interval) {
        return new Promise(async (resolve) => {
            // 立即执行第一次采集
            await collectPage(filters, pageSize);

            // 如果第一页就没有数据，直接完成
            if (!isCollecting) {
                resolve();
                return;
            }

            // 设置定时采集，并保存resolve函数供后续调用
            window.currentShopResolve = resolve;
            collectionInterval = setInterval(async () => {
                if (isCollecting) {
                    await collectPage(filters, pageSize);
                }
            }, interval);
        });
    }

    // 连续采集（原逻辑，用于没有店铺列表的情况）
    function startContinuousCollection(filters, pageSize, interval) {
        // 立即执行第一次采集
        collectPage(filters, pageSize);

        // 设置定时采集
        collectionInterval = setInterval(async () => {
            if (isCollecting) {
                await collectPage(filters, pageSize);
            }
        }, interval);
    }

    // 停止采集
    function stopCollection() {
        if (!isCollecting && !isPaused) return;

        isCollecting = false;
        isPaused = false;

        if (collectionInterval) {
            clearInterval(collectionInterval);
            collectionInterval = null;
        }

        // 清理店铺采集的resolve函数
        if (window.currentShopResolve) {
            window.currentShopResolve();
            window.currentShopResolve = null;
        }

        // 清空采集进度
        collectionProgress = {
            filters: null,
            pageSize: 30,
            interval: 3000,
            currentPageNum: 1,
            currentShopIndex: 0,
            shopKeywords: [],
            selectedAnchor: null,
            selectedTag: null,
            collectedProducts: []
        };

        updateCollectionStatus('已停止', false, false);
        addLog('采集已停止', 'info');

        if (shopKeywords.length > 0) {
            addLog(`总共采集了 ${shopKeywords.length} 个店铺，获得 ${collectedProducts.length} 个商品`, 'info');
        } else {
            addLog(`总共采集了 ${collectedProducts.length} 个商品`, 'info');
        }

        // 重置按钮文本
        document.getElementById('startCollectionBtn').innerHTML = '<i class="fas fa-play"></i>开始采集';

        // 如果没有采集到数据，显示提示信息
        if (collectedProducts.length === 0) {
            const tbody = document.getElementById('productTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-gray-500 py-8">
                        <i class="fas fa-inbox text-4xl mb-2 block"></i>
                        暂无数据，请开始采集
                    </td>
                </tr>
            `;
        }
    }

    // 采集单页数据
    async function collectPage(filters, pageSize) {
        if (isRequesting) {
            console.log('已有请求在进行中，跳过本次请求');
            return;
        }

        try {
            isRequesting = true;
            const requestId = Date.now() + '-' + Math.random().toString(36).substring(2, 11);
            addLog(`正在采集第 ${currentPageNum} 页... (请求ID: ${requestId})`, 'info');

            // 构建官方格式的请求数据
            const requestData = buildRequestData(filters, currentPageNum, pageSize);

            // 打印完整的请求数据到控制台
            console.log(`[${requestId}] 发送请求的完整数据:`, JSON.stringify(requestData, null, 2));

            const response = await fetch('/api/product-collection/collect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': localStorage.getItem('apiKey') || ''
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();
            console.log(`[${requestId}] 收到响应:`, result);

            if (!response.ok) {
                console.error(`HTTP错误 ${response.status}:`, response.statusText);
                console.error('错误响应:', result);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${result.error || '未知错误'}`);
            }
            
            if (result.success) {
                const products = result.data || [];

                // 添加详细的调试信息
                console.log(`[${requestId}] 采集结果详情:`, {
                    end: result.end,
                    originalCount: result.originalCount,
                    filteredCount: result.filteredCount,
                    productsLength: products.length,
                    currentPage: result.currentPage,
                    total: result.total
                });

                // 后端已经进行了佣金筛选，直接使用返回的数据
                collectedProducts = collectedProducts.concat(products);

                // 显示采集结果，包含原始数量和筛选后数量
                let logMessage = `第 ${currentPageNum} 页采集成功，获得 ${products.length} 个商品`;
                if (result.originalCount && result.filteredCount !== undefined) {
                    logMessage = `第 ${currentPageNum} 页采集成功，原始 ${result.originalCount} 个商品，筛选后 ${result.filteredCount} 个商品`;
                }
                addLog(logMessage, 'success');

                // 显示符合条件的商品信息
                products.forEach(product => {
                    addLog(`商品: ${product.itemId} | ${product.itemName} | ¥${product.itemPrice} | 佣金:${product.commission}`, 'product');
                });

                // 更新表格显示
                updateProductTable(products);

                // 更新统计信息
                document.getElementById('currentPage').textContent = currentPageNum;
                document.getElementById('collectedCount').textContent = collectedProducts.length;

                // 只根据原始数据量判断是否继续采集，忽略API的end字段
                if (result.originalCount > 0) {
                    currentPageNum++;
                    // 更新采集进度
                    updateCollectionProgress();
                    addLog(`继续采集下一页（当前页有 ${result.originalCount} 个原始商品，API end字段: ${result.end}）`, 'info');
                } else {
                    addLog(`当前页无原始数据，当前采集目标完成（API end字段: ${result.end}）`, 'info');

                    // 清除当前的定时器
                    if (collectionInterval) {
                        clearInterval(collectionInterval);
                        collectionInterval = null;
                    }

                    // 如果有店铺列表，通知当前店铺采集完成
                    if (shopKeywords.length > 0 && window.currentShopResolve) {
                        addLog(`店铺 ${shopKeywords[currentShopIndex]} 采集完成`, 'info');
                        window.currentShopResolve();
                        window.currentShopResolve = null;
                    } else {
                        // 没有店铺列表，直接停止采集
                        stopCollection();
                    }
                }
            } else {
                throw new Error(result.error || '采集失败');
            }

        } catch (error) {
            console.error('采集失败:', error);
            addLog(`第 ${currentPageNum} 页采集失败: ${error.message}`, 'error');

            // 如果是认证错误，停止采集
            if (error.message.includes('401') || error.message.includes('Cookie')) {
                stopCollection();
            }
        } finally {
            isRequesting = false;
        }
    }

    // 获取选中的tag
    function getSelectedTag() {
        const activeTagBtn = document.querySelector('.tag-btn.active');
        return activeTagBtn ? activeTagBtn.dataset.tag : null;
    }



    // 获取选中的筛选条件
    function getSelectedFilters() {
        const filters = {};

        // 获取按钮筛选条件
        const activeButtons = document.querySelectorAll('.filter-button.active');

        activeButtons.forEach(button => {
            const filterType = button.dataset.filterType;
            const value = button.dataset.value;

            if (filterType === 'cateIds' || filterType === 'featured' || filterType === 'icTags') {
                // 多选类型，用逗号分隔
                if (!filters[filterType]) {
                    filters[filterType] = [];
                }
                filters[filterType].push(value);
            } else {
                // 单选类型
                filters[filterType] = value;
            }
        });

        // 处理多选类型的值
        if (filters.cateIds) {
            filters.cateIds = filters.cateIds.join(',');
        } else {
            // 如果没有选择类目，设置为空字符串
            filters.cateIds = '';
        }
        if (filters.featured) {
            filters.featured = filters.featured.join(',');
        }
        if (filters.icTags) {
            filters.icTags = filters.icTags.join(',');
        }
        if (filters.shopLevel) {
            filters.shopLevel = filters.shopLevel.join(',');
        }
        if (filters.shopType) {
            filters.shopType = filters.shopType.join(',');
        }

        // 获取区间输入框的值
        // 商品价格
        const priceMin = document.getElementById('priceMin').value;
        const priceMax = document.getElementById('priceMax').value;
        if (priceMin || priceMax) {
            const min = priceMin || '0';
            const max = priceMax || '999999';
            filters.priceSelect = `${min}_${max}`;
        }

        // 佣金比例
        const commissionMin = document.getElementById('commissionMin').value;
        const commissionMax = document.getElementById('commissionMax').value;
        if (commissionMin || commissionMax) {
            const min = commissionMin || '0';
            const max = commissionMax || '100';
            filters.commissionRateSelect = `${min}_${max}`;
        }

        // 365天销量
        const salesMin = document.getElementById('salesMin').value;
        const salesMax = document.getElementById('salesMax').value;
        if (salesMin || salesMax) {
            const min = salesMin || '0';
            const max = salesMax || '999999';
            filters.soldQuantity365Select = `${min}_${max}`;
        }

        // 佣金范围（用于后端筛选）
        const commissionRangeMin = document.getElementById('commissionRangeMin').value;
        const commissionRangeMax = document.getElementById('commissionRangeMax').value;
        if (commissionRangeMin || commissionRangeMax) {
            filters.commissionRange = {
                min: commissionRangeMin ? parseFloat(commissionRangeMin) : 0,
                max: commissionRangeMax ? parseFloat(commissionRangeMax) : 999999
            };
        }

        return filters;
    }

    // 获取选中分类的 code 值
    function getSelectedCodes() {
        const selectedCodes = [];
        const activeButtons = document.querySelectorAll('.filter-button.active[data-filter-type="cateIds"]');

        activeButtons.forEach(button => {
            const code = button.dataset.code;
            if (code) {
                selectedCodes.push(code);
            }
        });

        return selectedCodes.join(',');
    }

    // 构建请求数据的统一函数
    function buildRequestData(filters, pageNum = 1, pageSize = 30) {
        const requestData = {
            pageNum: pageNum,
            pageSize: pageSize,
            filedKey: "itemRankListInfo",
            rankType: "1",
            topnItemList: null,
            recommendTabType: 0,
            code: getSelectedCodes() || '0',
            anchorId: selectedAnchor ? selectedAnchor.anchor_id : null,
            tag: getSelectedTag(), // 添加tag参数
            commissionRange: filters.commissionRange, // 包含佣金范围用于后端筛选
            ...filters  // 将所有filters字段展开到根级别
        };

        // 如果有keyword参数，添加到请求数据中
        if (filters.keyword) {
            requestData.keyword = filters.keyword;
        }

        return requestData;
    }

    // 显示条件数据
    function displayConditionData(filters) {
        const container = document.getElementById('conditionDataContainer');
        const display = document.getElementById('conditionDataDisplay');

        // 构建官方格式的扁平化请求数据
        const pageSize = parseInt(document.getElementById('pageSizeSelect').value);
        const requestData = buildRequestData(filters, 1, pageSize);

        // 格式化JSON并添加语法高亮
        const formattedJson = formatJsonWithHighlight(requestData);
        display.innerHTML = formattedJson;

        // 显示容器
        container.style.display = 'block';
    }

    // 格式化JSON并添加语法高亮（压缩格式）
    function formatJsonWithHighlight(obj) {
        // 使用压缩格式，不添加换行和缩进
        const jsonString = JSON.stringify(obj);

        return jsonString
            .replace(/("[\w\d_]+")(\s*:)/g, '<span class="json-key">$1</span>$2')
            .replace(/:\s*(".*?")/g, ': <span class="json-string">$1</span>')
            .replace(/:\s*(\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
            .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
            .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>');
    }

    // 切换条件数据显示/隐藏
    function toggleConditionData() {
        const container = document.getElementById('conditionDataContainer');
        const content = container.querySelector('.card-body');
        const btn = document.getElementById('toggleConditionBtn');
        const icon = btn.querySelector('i');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.className = 'fas fa-eye-slash';
            btn.innerHTML = '<i class="fas fa-eye-slash"></i>隐藏';
        } else {
            content.style.display = 'none';
            icon.className = 'fas fa-eye';
            btn.innerHTML = '<i class="fas fa-eye"></i>显示';
        }
    }

    // 更新采集状态
    function updateCollectionStatus(status, isActive, isPausedState = false) {
        document.getElementById('collectionStatus').textContent = status;
        document.getElementById('startCollectionBtn').disabled = isActive;
        document.getElementById('pauseCollectionBtn').disabled = !isActive;
        document.getElementById('stopCollectionBtn').disabled = !isActive && !isPausedState;
    }

    // 添加日志
    function addLog(message, type = 'info') {
        const logContainer = document.getElementById('logContainer');
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // 清空日志
    function clearLog() {
        document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">日志已清空</div>';
    }

    // 更新商品表格
    function updateProductTable(products, isAppend = true) {
        const tbody = document.getElementById('productTableBody');
        const tableWrapper = document.querySelector('.table-wrapper');

        // 如果不是追加模式，清除现有内容
        if (!isAppend) {
            tbody.innerHTML = '';
        }

        // 如果是第一次添加数据（表格为空），也要清除"暂无数据"提示
        if (isAppend && tbody.children.length === 1 && tbody.children[0].cells.length === 1) {
            tbody.innerHTML = '';
        }

        products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.itemId}</td>
                <td title="${product.itemName}">${truncateText(product.itemName, 30)}</td>
                <td>¥${product.itemPrice}</td>
                <td>${product.commission}</td>
                <td>${product.soldQuantity30}</td>
                <td>${product.soldQuantity365}</td>
                <td>${product.shopName}</td>
            `;
            tbody.appendChild(row);
        });

        // 自动滚动到表格底部，就像实时日志一样
        if (tableWrapper) {
            tableWrapper.scrollTop = tableWrapper.scrollHeight;
        }
    }

    // 截断文本
    function truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
});
