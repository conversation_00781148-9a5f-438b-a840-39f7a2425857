// 选品规则配置系统
class SelectionRuleManager {
    constructor() {
        this.currentRule = {
            id: null,  // 新增：当前规则的ID，用于区分创建和更新
            name: '',
            description: '',
            totalTarget: 500,
            groups: [],
            // 新增综合配置
            comprehensiveConfig: {
                deduplicationEnabled: true,
                shuffleGroups: []  // 新增：需要随机打乱的规则组索引数组
            }
        };
        this.selectedGroupIndex = -1;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadExecutionHistory();
        this.initComprehensiveConfig();
        this.initFilterModule();
        // 初始化空的预览结果
        this.renderPreviewResults({ products: [], total: 0 });
        // 不添加默认组，让用户自己创建
    }

    bindEvents() {
        // 基本信息事件
        $('#ruleName').on('input', (e) => {
            this.currentRule.name = e.target.value;
        });

        $('#ruleDescription').on('input', (e) => {
            this.currentRule.description = e.target.value;
        });

        $('#totalTarget').on('input', (e) => {
            this.currentRule.totalTarget = parseInt(e.target.value) || 500;
            $('#totalTargetDisplay').text(this.currentRule.totalTarget);
            this.updateComprehensiveConfig();
        });

        // 添加字段焦点提示，让用户知道字段是可编辑的
        $('#ruleName, #ruleDescription, #totalTarget').on('focus', function() {
            $(this).addClass('ring-2 ring-blue-500');
        }).on('blur', function() {
            $(this).removeClass('ring-2 ring-blue-500');
        });

        // 按钮事件
        $('#addGroupBtn').click(() => this.addGroup());
        $('#newRuleBtn').click(() => this.createNewRule());
        $('#saveRuleBtn').click(() => this.showSaveModal());
        $('#loadRuleBtn').click(() => this.loadSelectedRule());
        $('#executeRuleBtn').click(() => this.executeRule());
        $('#copyPreviewBtn').click(() => this.copyPreviewResults());

        // 模态框事件
        $('#cancelSaveBtn').click(() => this.hideModals());
        $('#confirmSaveBtn').click(() => this.saveRule());

        // 点击模态框背景关闭
        $('#saveRuleModal').click((e) => {
            if (e.target === e.currentTarget) {
                this.hideModals();
            }
        });
    }

    addGroup(name = '', target = 50, conditions = []) {
        const group = {
            name: name || `规则组${this.currentRule.groups.length + 1}`,
            target: target,
            conditions: conditions.length > 0 ? conditions : [
                { field: 'tag', operator: '=', value: '' }
            ],
            orderBy: 'sales_7_days_growth_rate',
            orderDirection: 'DESC',
            // 新增位置和优先级配置
            positionConfig: {
                priority: this.currentRule.groups.length + 1,
                startPosition: this.calculateNextStartPosition(),
                endPosition: this.calculateNextEndPosition(target),
                randomSelection: false  // 新增随机选取配置
            }
        };

        this.currentRule.groups.push(group);
        this.renderGroupsList();
        this.updateTotalDisplay();
        this.updateComprehensiveConfig();

        // 自动选中新添加的组
        this.selectGroup(this.currentRule.groups.length - 1);
    }

    calculateNextStartPosition() {
        if (this.currentRule.groups.length === 0) return 1;
        
        const lastGroup = this.currentRule.groups[this.currentRule.groups.length - 1];
        // 确保positionConfig存在
        if (!lastGroup.positionConfig) {
            lastGroup.positionConfig = {
                priority: this.currentRule.groups.length,
                startPosition: 1,
                endPosition: lastGroup.target || 50,
                allowOverlap: false
            };
        }
        return lastGroup.positionConfig.endPosition + 1;
    }

    calculateNextEndPosition(target) {
        const startPos = this.calculateNextStartPosition();
        return startPos + target - 1;
    }

    // 确保组具有完整的位置配置
    ensureGroupPositionConfig(group, index) {
        if (!group.positionConfig) {
            const startPos = index === 0 ? 1 : this.calculateStartPositionForIndex(index);
            group.positionConfig = {
                priority: index + 1,
                startPosition: startPos,
                endPosition: startPos + (group.target || 50) - 1,
                randomSelection: false  // 确保随机选取配置存在
            };
        } else {
            // 确保随机选取配置存在
            if (typeof group.positionConfig.randomSelection === 'undefined') {
                group.positionConfig.randomSelection = false;
            }
        }
        return group;
    }

    calculateStartPositionForIndex(index) {
        if (index === 0) return 1;
        
        let pos = 1;
        for (let i = 0; i < index; i++) {
            const prevGroup = this.currentRule.groups[i];
            
            // 安全检查：确保组存在
            if (!prevGroup) {
                console.warn(`组 ${i} 不存在，跳过位置计算`);
                continue;
            }
            
            // 如果有完整的位置配置，使用endPosition
            if (prevGroup.positionConfig && typeof prevGroup.positionConfig.endPosition === 'number') {
                pos = prevGroup.positionConfig.endPosition + 1;
            } else {
                // 否则使用target数量累加
                const target = prevGroup.target || 50;
                pos += target;
            }
        }
        return pos;
    }

    selectGroup(index) {
        this.selectedGroupIndex = index;
        this.renderGroupsList();
        this.renderGroupConfig();
        this.updateComprehensiveConfig();
        this.previewGroupResults();
    }

    removeGroup(index) {
        if (this.currentRule.groups.length <= 1) {
            layer.msg('至少需要保留一个规则组');
            return;
        }

        this.currentRule.groups.splice(index, 1);

        // 调整选中索引
        if (this.selectedGroupIndex >= index) {
            this.selectedGroupIndex = Math.max(0, this.selectedGroupIndex - 1);
        }

        // 重新计算所有组的位置
        this.recalculatePositions();

        this.renderGroupsList();
        this.updateTotalDisplay();
        this.updateComprehensiveConfig();

        if (this.currentRule.groups.length > 0) {
            this.renderGroupConfig();
            this.previewGroupResults();
        }
    }

    recalculatePositions() {
        let currentPos = 1;
        this.currentRule.groups.forEach((group, index) => {
            // 确保positionConfig存在
            if (!group.positionConfig) {
                group.positionConfig = {
                    priority: index + 1,
                    startPosition: currentPos,
                    endPosition: currentPos + (group.target || 50) - 1
                };
            } else {
                group.positionConfig.priority = index + 1;
                group.positionConfig.startPosition = currentPos;
                group.positionConfig.endPosition = currentPos + (group.target || 50) - 1;
            }
            currentPos += group.target || 50;
        });
    }

    renderGroupsList() {
        const container = $('#ruleGroupsList');
        container.empty();

        if (this.currentRule.groups.length === 0) {
            container.html(`
                <div class="empty-state text-center text-gray-500 py-8 rounded-lg">
                    <i class="fas fa-plus-circle text-4xl mb-4 text-gray-300"></i>
                    <p class="text-sm font-medium">暂无规则组</p>
                    <p class="text-xs text-gray-400 mt-2">点击"添加"创建第一个规则组</p>
                </div>
            `);
            return;
        }

        // 创建带有原始索引的组数组，然后按优先级排序
        const groupsWithIndex = this.currentRule.groups.map((group, index) => {
            // 确保组具有完整的位置配置
            const ensuredGroup = this.ensureGroupPositionConfig(group, index);
            return {
                group: ensuredGroup,
                originalIndex: index
            };
        });

        // 按优先级升序排序（优先级小的在前）
        groupsWithIndex.sort((a, b) => {
            const priorityA = a.group.positionConfig?.priority || 999;
            const priorityB = b.group.positionConfig?.priority || 999;
            return priorityA - priorityB;
        });

        // 渲染排序后的组
        groupsWithIndex.forEach(({ group, originalIndex }) => {
            const isActive = originalIndex === this.selectedGroupIndex;
            const validConditions = group.conditions.filter(c => c.field && c.value !== '').length;
            const positionRange = `${group.positionConfig.startPosition}-${group.positionConfig.endPosition}`;
            const randomIcon = group.positionConfig.randomSelection ? '<i class="fas fa-random text-orange-500 ml-1" title="随机选取"></i>' : '';
            
            const card = $(`
                <div class="rule-group-card ${isActive ? 'active' : ''} bg-white border rounded-lg p-3 cursor-pointer hover:shadow-md transition-all">
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex-1 min-w-0">
                            <h4 class="font-medium text-gray-900 truncate">${group.name}${randomIcon}</h4>
                            <div class="flex items-center space-x-3 mt-1">
                                <span class="text-sm text-blue-600 font-medium">${group.target}</span>
                                <span class="text-xs text-gray-500">${validConditions}/${group.conditions.length} 条件</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">位置: ${positionRange}</span>
                                <span class="text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded">优先级: ${group.positionConfig.priority}</span>
                                ${group.positionConfig.randomSelection ? '<span class="text-xs bg-orange-100 text-orange-600 px-2 py-0.5 rounded">随机</span>' : ''}
                            </div>
                        </div>
                        <div class="flex space-x-1 ml-2">
                            <button class="text-gray-400 hover:text-red-500 p-1 rounded hover:bg-red-50 transition-colors" onclick="ruleManager.removeGroup(${originalIndex})" title="删除规则组">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                            <div class="progress-bar h-1.5 rounded-full transition-all" style="width: ${validConditions > 0 ? (validConditions / group.conditions.length * 100) : 0}%"></div>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-500">配置完成度</span>
                            <span class="text-gray-600">${Math.round(validConditions / group.conditions.length * 100)}%</span>
                        </div>
                    </div>
                </div>
            `);

            card.click((e) => {
                if (!$(e.target).closest('button').length) {
                    this.selectGroup(originalIndex);
                }
            });
            container.append(card);
        });
    }

    renderGroupConfig() {
        if (this.selectedGroupIndex < 0 || !this.currentRule.groups[this.selectedGroupIndex]) {
            $('#ruleConfigArea').html(`
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-mouse-pointer text-3xl mb-3"></i>
                    <p>请选择左侧的规则组进行配置</p>
                </div>
            `);
            return;
        }

        const group = this.currentRule.groups[this.selectedGroupIndex];
        const configHtml = `
            <div class="space-y-6">
                <!-- 基本设置 -->
                <div class="config-section rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">基本设置</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">组名称</label>
                            <input type="text" id="groupName" value="${group.name}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="如：大类目组">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标数量</label>
                            <input type="number" id="groupTarget" value="${group.target}" min="1" max="1000"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="选品数量">
                        </div>
                    </div>
                </div>

                <!-- 位置和优先级设置 -->
                <div class="config-section rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">位置和优先级</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                            <input type="number" id="groupPriority" value="${group.positionConfig.priority}" min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="数字越小优先级越高">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">起始位置</label>
                            <input type="number" id="groupStartPos" value="${group.positionConfig.startPosition}" min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="起始位置">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">结束位置</label>
                            <input type="number" id="groupEndPos" value="${group.positionConfig.endPosition}" min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="结束位置">
                        </div>
                    </div>
                    <div class="mt-3 space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="randomSelection" ${group.positionConfig.randomSelection ? 'checked' : ''} class="mr-2">
                            <span class="text-sm text-gray-700">随机选取产品</span>
                        </label>
                        <p class="text-xs text-gray-500 ml-6">勾选后，从符合条件的产品中随机选择指定数量，每次执行结果可能不同</p>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-sm font-medium text-gray-900">筛选条件</h4>
                        <button id="addConditionBtn" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-md text-sm transition-colors">
                            <i class="fas fa-plus mr-1"></i>添加条件
                        </button>
                    </div>
                    <div id="conditionsList" class="space-y-3">
                        ${this.renderConditions(group.conditions)}
                    </div>
                    ${group.conditions.length === 0 ? `
                        <div class="text-center text-gray-500 py-6 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="fas fa-filter text-2xl mb-2 text-gray-300"></i>
                            <p class="text-sm">暂无筛选条件</p>
                            <p class="text-xs text-gray-400 mt-1">点击"添加条件"开始配置</p>
                        </div>
                    ` : ''}
                </div>

                <!-- 排序设置 -->
                <div class="config-section rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">排序设置</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">排序字段</label>
                            <select id="orderBy" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                ${this.getOrderByOptions(group.orderBy)}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">排序方向</label>
                            <select id="orderDirection" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="DESC" ${group.orderDirection === 'DESC' ? 'selected' : ''}>降序 (高到低)</option>
                                <option value="ASC" ${group.orderDirection === 'ASC' ? 'selected' : ''}>升序 (低到高)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="text-xs text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            ${group.positionConfig.randomSelection ? 
                                '注意：启用随机选取后，排序设置仅影响筛选后的候选产品池，最终结果将从中随机选择' : 
                                '排序设置将直接影响最终选品结果的顺序'}
                        </p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-3">
                    <button id="previewBtn" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2.5 rounded-md transition-colors font-medium">
                        <i class="fas fa-search mr-2"></i>预览结果
                    </button>
                    <button id="resetBtn" class="px-4 bg-gray-500 hover:bg-gray-600 text-white py-2.5 rounded-md transition-colors">
                        <i class="fas fa-undo mr-1"></i>重置
                    </button>
                </div>
            </div>
        `;

        $('#ruleConfigArea').html(configHtml);
        this.bindGroupConfigEvents();
    }

    // 初始化综合配置
    initComprehensiveConfig() {
        this.updateComprehensiveConfig();
    }

    // 更新综合配置显示
    updateComprehensiveConfig() {
        const configHtml = `
            <div class="space-y-6">
                <!-- 配置策略 -->
                <div class="config-section rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">配置策略</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="deduplicationEnabled" ${this.currentRule.comprehensiveConfig.deduplicationEnabled ? 'checked' : ''} class="mr-2">
                                <span class="text-sm text-gray-700">启用去重</span>
                            </label>
                            <p class="text-xs text-gray-500 mt-1">低优先级组自动排除高优先级组已选产品</p>
                        </div>
                        
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="shuffleGroups" ${this.currentRule.comprehensiveConfig.shuffleGroups.length > 0 ? 'checked' : ''} class="mr-2">
                                <span class="text-sm text-gray-700">跨组打乱顺序</span>
                            </label>
                            <p class="text-xs text-gray-500 mt-1">勾选后，选择的规则组产品将混合随机排列，突破组间界限</p>
                        </div>
                        
                        <!-- 规则组选择 -->
                        <div id="shuffleGroupsSelection" class="mt-3 ${this.currentRule.comprehensiveConfig.shuffleGroups.length > 0 ? '' : 'hidden'}">
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择要跨组打乱的规则组</label>
                            <div class="space-y-2 max-h-32 overflow-y-auto">
                                ${this.renderShuffleGroupOptions()}
                            </div>
                            <p class="text-xs text-gray-500 mt-1">至少选择2个规则组才能进行跨组打乱</p>
                        </div>
                    </div>
                </div>

                <!-- 位置分布图 -->
                <div class="config-section rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">位置分布图</h4>
                    <div id="positionChart" class="space-y-2">
                        ${this.renderPositionChart()}
                    </div>
                </div>

                <!-- 规则组摘要 -->
                <div class="config-section rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">规则组摘要</h4>
                    <div id="groupSummary" class="space-y-2">
                        ${this.renderGroupSummary()}
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-3">
                    <button id="previewAllBtn" class="flex-1 bg-purple-500 hover:bg-purple-600 text-white py-2.5 rounded-md transition-colors font-medium">
                        <i class="fas fa-eye mr-2"></i>预览全部
                    </button>
                    <button id="optimizeBtn" class="px-4 bg-orange-500 hover:bg-orange-600 text-white py-2.5 rounded-md transition-colors">
                        <i class="fas fa-magic mr-1"></i>智能优化
                    </button>
                </div>
            </div>
        `;

        $('#comprehensiveConfigArea').html(configHtml);
        this.bindComprehensiveConfigEvents();
        
        // 更新HTML中的分布图表
        if (typeof updateDistributionData === 'function') {
            updateDistributionData(this.currentRule.groups);
        }
    }

    // 渲染位置分布图
    renderPositionChart() {
        if (this.currentRule.groups.length === 0) {
            return '<div class="text-center text-gray-500 py-4">暂无规则组</div>';
        }

        const totalTarget = this.currentRule.totalTarget;
        
        // 创建带有位置配置的组数组副本，然后按优先级排序
        const sortedGroups = this.currentRule.groups.map((group, index) => {
            // 确保组具有完整的位置配置
            return this.ensureGroupPositionConfig(group, index);
        }).sort((a, b) => {
            // 按优先级升序排序（优先级小的在前）
            const priorityA = a.positionConfig?.priority || 999;
            const priorityB = b.positionConfig?.priority || 999;
            return priorityA - priorityB;
        });

        // 定义颜色数组
        const colors = [
            { bg: 'bg-blue-500', text: '流量品' },
            { bg: 'bg-green-500', text: '大类目品' },
            { bg: 'bg-yellow-500', text: '出单同类品' },
            { bg: 'bg-red-500', text: '店铺品' },
            { bg: 'bg-purple-500', text: '规则组5' },
            { bg: 'bg-pink-500', text: '规则组6' },
            { bg: 'bg-indigo-500', text: '规则组7' },
            { bg: 'bg-orange-500', text: '规则组8' }
        ];

        // 生成图例
        const legendHtml = sortedGroups.map((group, index) => {
            const color = colors[index % colors.length];
            const startPos = group.positionConfig.startPosition;
            const endPos = group.positionConfig.endPosition;
            
            return `
                <div class="flex items-center space-x-2 text-sm">
                    <div class="w-4 h-4 ${color.bg} rounded"></div>
                    <span class="font-medium">${group.name}</span>
                    <span class="text-gray-500">(${startPos}-${endPos})</span>
                </div>
            `;
        }).join('');

        // 生成进度条分段
        const progressSegments = sortedGroups.map((group, index) => {
            const startPos = group.positionConfig.startPosition;
            const endPos = group.positionConfig.endPosition;
            const width = ((endPos - startPos + 1) / totalTarget) * 100;
            const left = ((startPos - 1) / totalTarget) * 100;
            const color = colors[index % colors.length];
            
            return `
                <div class="${color.bg} h-full absolute flex items-center justify-center text-xs text-white font-medium" 
                     style="left: ${left}%; width: ${width}%"
                     title="${group.name}: ${startPos}-${endPos} (${group.target}个)">
                    ${group.target}
                </div>
            `;
        }).join('');

        return `
            <!-- 图例 -->
            <div class="grid grid-cols-2 gap-2 mb-3">
                ${legendHtml}
            </div>
            
            <!-- 进度条 -->
            <div class="relative h-8 bg-gray-200 rounded-lg overflow-hidden">
                ${progressSegments}
            </div>
            
            <!-- 位置标尺 -->
            <div class="flex justify-between text-xs text-gray-500 mt-2">
                <span>1</span>
                <span>${Math.floor(totalTarget/4)}</span>
                <span>${Math.floor(totalTarget/2)}</span>
                <span>${Math.floor(totalTarget*3/4)}</span>
                <span>${totalTarget}</span>
            </div>
        `;
    }

    // 渲染规则组摘要
    renderGroupSummary() {
        if (this.currentRule.groups.length === 0) {
            return '<div class="text-center text-gray-500 py-4">暂无规则组</div>';
        }

        // 创建带有位置配置的组数组副本，然后按优先级排序
        const sortedGroups = this.currentRule.groups.map((group, index) => {
            // 确保组具有完整的位置配置
            return this.ensureGroupPositionConfig(group, index);
        }).sort((a, b) => {
            // 按优先级升序排序（优先级小的在前）
            const priorityA = a.positionConfig?.priority || 999;
            const priorityB = b.positionConfig?.priority || 999;
            return priorityA - priorityB;
        });

        return sortedGroups.map((group) => {
            const validConditions = group.conditions.filter(c => c.field && c.value !== '').length;
            const completeness = Math.round((validConditions / group.conditions.length) * 100);
            const randomIcon = group.positionConfig.randomSelection ? 
                '<i class="fas fa-random text-orange-500 ml-2" title="随机选取"></i>' : '';
            
            return `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <div>
                            <div class="font-medium text-sm">${group.name}${randomIcon}</div>
                            <div class="text-xs text-gray-500">
                                优先级: ${group.positionConfig.priority} | 
                                位置: ${group.positionConfig.startPosition}-${group.positionConfig.endPosition} | 
                                数量: ${group.target}
                                ${group.positionConfig.randomSelection ? ' | 随机选取' : ''}
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium ${completeness === 100 ? 'text-green-600' : 'text-yellow-600'}">
                            ${completeness}%
                        </div>
                        <div class="text-xs text-gray-500">完成度</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 绑定综合配置事件
    bindComprehensiveConfigEvents() {
        // 去重设置变化
        $('#deduplicationEnabled').on('change', (e) => {
            this.currentRule.comprehensiveConfig.deduplicationEnabled = e.target.checked;
        });

        // 预览全部按钮
        $('#previewAllBtn').click(() => {
            this.previewAllResults();
        });

        // 智能优化按钮
        $('#optimizeBtn').click(() => {
            this.optimizeConfiguration();
        });

        // 随机打乱设置变化
        $('#shuffleGroups').on('change', (e) => {
            const isEnabled = e.target.checked;
            
            if (isEnabled) {
                // 检查是否有足够的规则组
                if (this.currentRule.groups.length < 2) {
                    layer.msg('至少需要2个规则组才能启用跨组打乱');
                    $('#shuffleGroups').prop('checked', false);
                    return;
                }
                
                // 显示规则组选择界面
                $('#shuffleGroupsSelection').removeClass('hidden');
                
                // 默认选中前两个规则组
                this.currentRule.comprehensiveConfig.shuffleGroups = [0, 1];
                
                // 更新界面
                this.updateComprehensiveConfig();
                
                layer.msg('已启用跨组打乱，请选择要打乱的规则组');
            } else {
                // 隐藏规则组选择界面
                $('#shuffleGroupsSelection').addClass('hidden');
                this.currentRule.comprehensiveConfig.shuffleGroups = [];
            }
        });
        
        // 绑定规则组选择事件
        this.bindShuffleGroupEvents();
    }

    // 预览全部结果
    async previewAllResults() {
        if (this.currentRule.groups.length === 0) {
            layer.msg('请先添加规则组');
            return;
        }

        const loadingIndex = layer.load(1);
        const anchorId = $('#anchorFilter').val();
        const date = $('#dateFilter').val();

        try {
            const requestData = {
                groups: this.currentRule.groups,
                comprehensiveConfig: this.currentRule.comprehensiveConfig,
                totalTarget: this.currentRule.totalTarget
            };

            // 如果有筛选条件，添加到请求中
            if (anchorId && date) {
                requestData.anchor_id = anchorId;
                requestData.date = date;
                console.log('预览全部结果 - 应用筛选条件:', { anchorId, date });
            }

            const response = await fetch('/api/selection-rules/preview-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();
            layer.close(loadingIndex);

            if (result.success) {
                this.renderComprehensivePreview(result.data);
            } else {
                layer.msg('预览失败: ' + result.message);
            }
        } catch (error) {
            layer.close(loadingIndex);
            console.error('预览失败:', error);
            layer.msg('预览失败，请检查网络连接');
        }
    }

    // 智能优化配置
    optimizeConfiguration() {
        if (this.currentRule.groups.length === 0) {
            layer.msg('请先添加规则组');
            return;
        }

        layer.confirm('智能优化将自动调整各组的位置和优先级，是否继续？', {
            icon: 3,
            title: '智能优化'
        }, (index) => {
            layer.close(index);
            this.doOptimizeConfiguration();
        });
    }

    doOptimizeConfiguration() {
        // 按照当前目标数量重新分配位置
        const totalTarget = this.currentRule.totalTarget;
        const groupCount = this.currentRule.groups.length;
        
        // 平均分配基础数量
        const baseAmount = Math.floor(totalTarget / groupCount);
        const remainder = totalTarget % groupCount;
        
        let currentPos = 1;
        
        this.currentRule.groups.forEach((group, index) => {
            // 分配数量（前面的组多分配余数）
            const targetAmount = baseAmount + (index < remainder ? 1 : 0);
            
            group.target = targetAmount;
            
            // 确保positionConfig存在
            if (!group.positionConfig) {
                group.positionConfig = {
                    priority: index + 1,
                    startPosition: currentPos,
                    endPosition: currentPos + targetAmount - 1,
                    randomSelection: false
                };
            } else {
                group.positionConfig.priority = index + 1;
                group.positionConfig.startPosition = currentPos;
                group.positionConfig.endPosition = currentPos + targetAmount - 1;
            }
            
            currentPos += targetAmount;
        });
        
        // 重新渲染界面
        this.renderGroupsList();
        this.renderGroupConfig();
        this.updateComprehensiveConfig();
        this.updateTotalDisplay();
        
        layer.msg('智能优化完成');
    }

    // 渲染综合预览结果
    renderComprehensivePreview(data) {
        const { groups, total, conflicts, statistics } = data;
        
        // 更新预览区域
        $('#previewCount').text(total || 0);
        $('#previewScore').text((statistics ? statistics.overallScore : 0) + '%');
        
        // 显示冲突信息
        if (conflicts && conflicts.length > 0) {
            const conflictHtml = conflicts.map(conflict => 
                `<div class="text-sm text-red-600 mb-1">⚠️ ${conflict.message}</div>`
            ).join('');
            
            $('#conflictInfo').html(conflictHtml).removeClass('hidden');
        } else {
            $('#conflictInfo').addClass('hidden');
        }
        
        // 显示各组统计
        if (groups && groups.length > 0) {
            const groupStatsHtml = groups.map(group => `
                <div class="flex justify-between items-center py-2 border-b">
                    <span class="font-medium">${group.name}</span>
                    <div class="text-sm text-gray-600">
                        目标: ${group.target || 0} | 实际: ${group.actualCount || 0} | 覆盖率: ${group.coverage || 0}%
                        ${group.deduplicatedCount ? ` | 去重: ${group.deduplicatedCount}` : ''}
                        ${group.products && group.products.some(p => p.is_supplement) ? ' | 含补充产品' : ''}
                    </div>
                </div>
            `).join('');
            
            $('#groupStats').html(groupStatsHtml);
        } else {
            $('#groupStats').html('<div class="text-center text-gray-500 py-4">暂无组统计数据</div>');
        }
        
        // 显示产品列表
        if (groups && groups.length > 0) {
            // 合并所有组的产品
            let allProducts = [];

            groups.forEach(group => {
                if (group.products && group.products.length > 0) {
                    allProducts = [...allProducts, ...group.products];
                }
            });

            // 按照 global_position 排序（这是关键修复）
            allProducts.sort((a, b) => {
                const posA = a.global_position || 0;
                const posB = b.global_position || 0;
                return posA - posB;
            });

            console.log('前端排序后的产品位置:', allProducts.slice(0, 10).map(p => ({
                id: p.product_id,
                group: p.group_name,
                priority: p.priority,
                global_position: p.global_position,
                position_in_group: p.position_in_group
            })));

            // 更新实际产品数量
            const actualTotal = allProducts.length;
            $('#previewCount').text(actualTotal);
            
            // 检查是否有补充产品，如果有则显示提示
            const supplementCount = allProducts.filter(p => p.is_supplement).length;
            if (supplementCount > 0) {
                layer.msg(`数量保证功能已生效，从优先级1组补充了 ${supplementCount} 个产品`, { 
                   
                    time: 3000 
                });
            }
            
            // 检查是否有去重的产品
            const totalDeduplicatedCount = groups.reduce((sum, group) => sum + (group.deduplicatedCount || 0), 0);
            if (totalDeduplicatedCount > 0) {
                layer.msg(`去重功能已生效，共去重了 ${totalDeduplicatedCount} 个产品`, { 
                   
                    time: 3000 
                });
            }
            
            this.renderPreviewResults({ products: allProducts, total: actualTotal });
            
           
        } else {
            // 显示空状态
            this.renderPreviewResults({ products: [], total: 0 });
        }
    }

    renderConditions(conditions) {
        return conditions.map((condition, index) => {
            const valueInput = this.getValueInput(condition.field, condition.value, index);
            return `
                <div class="condition-row flex items-center space-x-2 mb-3">
                    <select class="condition-field flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" data-index="${index}">
                        ${this.getFieldOptions(condition.field)}
                    </select>
                    <select class="condition-operator px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" data-index="${index}">
                        ${this.getOperatorOptions(condition.field, condition.operator)}
                    </select>
                    <div class="condition-value-container flex-1" data-index="${index}">
                        ${valueInput}
                    </div>
                    <button class="text-red-500 hover:text-red-700 p-2 rounded hover:bg-red-50" onclick="ruleManager.removeCondition(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }).join('');
    }

    getFieldOptions(selected) {
        const fields = [
            { value: 'tag', label: '标签' },
            { value: 'commission_amount', label: '佣金金额' },
            { value: 'product_price', label: '产品价格' },
            { value: 'sales_365_days', label: '365天销量' },
            { value: 'sales_7_days_growth_rate', label: '7天增长率' },
            { value: 'anchor_sales_30_days', label: '30天主播成交' },
            { value: 'product_source', label: '产品来源' },
            { value: 'alliance_channel', label: '联盟渠道' },
            { value: 'product_type', label: '产品类型' }
        ];

        return fields.map(field =>
            `<option value="${field.value}" ${field.value === selected ? 'selected' : ''}>${field.label}</option>`
        ).join('');
    }

    getOperatorOptions(field, selected) {
        // 根据字段类型返回合适的操作符
        const numericFields = ['commission_amount', 'product_price', 'sales_365_days', 'sales_7_days_growth_rate', 'anchor_sales_30_days', 'orders_30_days'];
        const selectFields = ['tag', 'product_source', 'alliance_channel', 'product_type'];

        let operators;
        if (numericFields.includes(field)) {
            operators = [
                { value: '>', label: '大于' },
                { value: '<', label: '小于' },
                { value: '>=', label: '大于等于' },
                { value: '<=', label: '小于等于' },
                { value: '=', label: '等于' },
                { value: '!=', label: '不等于' }
            ];
        } else if (selectFields.includes(field)) {
            operators = [
                { value: '=', label: '等于' },
                { value: '!=', label: '不等于' }
            ];
        } else {
            operators = [
                { value: '=', label: '等于' },
                { value: '!=', label: '不等于' },
                { value: 'LIKE', label: '包含' }
            ];
        }

        return operators.map(op =>
            `<option value="${op.value}" ${op.value === selected ? 'selected' : ''}>${op.label}</option>`
        ).join('');
    }

    getOrderByOptions(selected) {
        const fields = [
            { value: 'sales_7_days_growth_rate', label: '7天增长率' },
            { value: 'commission_amount', label: '佣金金额' },
            { value: 'sales_365_days', label: '365天销量' },
            { value: 'anchor_sales_30_days', label: '30天主播成交' },
            { value: 'orders_30_days', label: '30天订单数' },
            { value: 'product_price', label: '产品价格' },
            { value: 'created_at', label: '创建时间' }
        ];

        return fields.map(field =>
            `<option value="${field.value}" ${field.value === selected ? 'selected' : ''}>${field.label}</option>`
        ).join('');
    }

    getValueInput(field, value, index) {
        const baseClass = "condition-value w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500";

        switch (field) {
            case 'tag':
                return `
                    <select class="${baseClass}" data-index="${index}">
                        <option value="">请选择标签</option>
                        <option value="1" ${value == 1 ? 'selected' : ''}>大类目</option>
                        <option value="2" ${value == 2 ? 'selected' : ''}>流量品</option>
                        <option value="3" ${value == 3 ? 'selected' : ''}>店铺品</option>
                        <option value="4" ${value == 4 ? 'selected' : ''}>出单同类品</option>
                    </select>
                `;

            case 'product_source':
                return `
                    <select class="${baseClass}" data-index="${index}">
                        <option value="">请选择产品来源</option>
                        <option value="热浪联盟" ${value === '热浪联盟' ? 'selected' : ''}>热浪联盟</option>
                        <option value="淘宝搜索" ${value === '淘宝搜索' ? 'selected' : ''}>淘宝搜索</option>
                        <option value="淘宝首页" ${value === '淘宝首页' ? 'selected' : ''}>淘宝首页</option>
                        <option value="淘宝秒杀" ${value === '淘宝秒杀' ? 'selected' : ''}>淘宝秒杀</option>
                        <option value="淘宝直播" ${value === '淘宝直播' ? 'selected' : ''}>淘宝直播</option>
                        <option value="淘宝百亿补贴" ${value === '淘宝百亿补贴' ? 'selected' : ''}>淘宝百亿补贴</option>
                        <option value="点击" ${value === '点击' ? 'selected' : ''}>点击</option>
                        <option value="订单" ${value === '订单' ? 'selected' : ''}>订单</option>
                    </select>
                `;

            case 'alliance_channel':
                return `
                    <select class="${baseClass}" data-index="${index}">
                        <option value="">请选择联盟渠道</option>
                        <option value="直播严选" ${value === '直播严选' ? 'selected' : ''}>直播严选</option>
                        <option value="天猫超市" ${value === '天猫超市' ? 'selected' : ''}>天猫超市</option>
                        <option value="源头优选" ${value === '源头优选' ? 'selected' : ''}>源头优选</option>
                        <option value="淘工厂" ${value === '淘工厂' ? 'selected' : ''}>淘工厂</option>
                        <option value="天天热卖" ${value === '天天热卖' ? 'selected' : ''}>天天热卖</option>
                        <option value="天猫U先" ${value === '天猫U先' ? 'selected' : ''}>天猫U先</option>
                        <option value="淘宝秒杀" ${value === '淘宝秒杀' ? 'selected' : ''}>淘宝秒杀</option>
                        <option value="淘宝买菜" ${value === '淘宝买菜' ? 'selected' : ''}>淘宝买菜</option>
                    </select>
                `;

            case 'product_type':
                return `
                    <select class="${baseClass}" data-index="${index}">
                        <option value="">请选择产品类型</option>
                        <option value="通用" ${value === '通用' ? 'selected' : ''}>通用</option>
                        <option value="严选" ${value === '严选' ? 'selected' : ''}>严选</option>
                    </select>
                `;

            case 'commission_amount':
                return `<input type="number" class="${baseClass}" value="${value}" data-index="${index}" placeholder="佣金金额" step="0.01" min="0">`;

            case 'product_price':
                return `<input type="number" class="${baseClass}" value="${value}" data-index="${index}" placeholder="产品价格" step="0.01" min="0">`;

            case 'sales_365_days':
                return `<input type="number" class="${baseClass}" value="${value}" data-index="${index}" placeholder="365天销量" min="0">`;

            case 'sales_7_days_growth_rate':
                return `<input type="number" class="${baseClass}" value="${value}" data-index="${index}" placeholder="7天增长率(%)" step="0.1">`;

            case 'anchor_sales_30_days':
                return `<input type="number" class="${baseClass}" value="${value}" data-index="${index}" placeholder="30天主播成交" step="0.01" min="0">`;

            case 'orders_30_days':
                return `<input type="number" class="${baseClass}" value="${value}" data-index="${index}" placeholder="30天订单数" min="0">`;

            default:
                return `<input type="text" class="${baseClass}" value="${value}" data-index="${index}" placeholder="请输入值">`;
        }
    }

    bindGroupConfigEvents() {
        const group = this.currentRule.groups[this.selectedGroupIndex];
        
        // 确保组具有完整的位置配置
        this.ensureGroupPositionConfig(group, this.selectedGroupIndex);

        // 组名称变化
        $('#groupName').on('input', (e) => {
            group.name = e.target.value;
            this.renderGroupsList();
            this.updateComprehensiveConfig();
        });

        // 目标数量变化
        $('#groupTarget').on('input', (e) => {
            const newTarget = parseInt(e.target.value) || 0;
            const oldTarget = group.target;
            group.target = newTarget;
            
            // 自动调整结束位置
            const positionDiff = newTarget - oldTarget;
            group.positionConfig.endPosition += positionDiff;
            
            // 更新后续组的位置
            for (let i = this.selectedGroupIndex + 1; i < this.currentRule.groups.length; i++) {
                const nextGroup = this.currentRule.groups[i];
                this.ensureGroupPositionConfig(nextGroup, i);
                nextGroup.positionConfig.startPosition += positionDiff;
                nextGroup.positionConfig.endPosition += positionDiff;
            }
            
            this.renderGroupsList();
            this.updateTotalDisplay();
            this.updateComprehensiveConfig();
        });

        // 优先级变化
        $('#groupPriority').on('input', (e) => {
            group.positionConfig.priority = parseInt(e.target.value) || 1;
            this.updateComprehensiveConfig();
        });

        // 位置变化
        $('#groupStartPos').on('input', (e) => {
            const newStart = parseInt(e.target.value) || 1;
            const positionDiff = newStart - group.positionConfig.startPosition;
            group.positionConfig.startPosition = newStart;
            group.positionConfig.endPosition += positionDiff;
            this.renderGroupsList();
            this.updateComprehensiveConfig();
        });

        $('#groupEndPos').on('input', (e) => {
            const newEnd = parseInt(e.target.value) || 1;
            group.positionConfig.endPosition = newEnd;
            group.target = newEnd - group.positionConfig.startPosition + 1;
            this.renderGroupsList();
            this.updateTotalDisplay();
            this.updateComprehensiveConfig();
        });

        // 随机选取变化
        $('#randomSelection').on('change', (e) => {
            group.positionConfig.randomSelection = e.target.checked;
            this.renderGroupsList();
            this.updateComprehensiveConfig();
            
            // 显示提示信息
            if (e.target.checked) {
                layer.msg('已启用随机选取，每次预览和执行结果可能不同');
            }
        });

        // 排序设置变化
        $('#orderBy').on('change', (e) => {
            group.orderBy = e.target.value;
        });

        $('#orderDirection').on('change', (e) => {
            group.orderDirection = e.target.value;
        });

        // 条件变化事件
        $('.condition-field').on('change', (e) => {
            const index = parseInt(e.target.dataset.index);
            const oldField = group.conditions[index].field;
            const newField = e.target.value;

            group.conditions[index].field = newField;

            // 如果字段类型改变，重置操作符和值
            if (oldField !== newField) {
                group.conditions[index].operator = '=';
                group.conditions[index].value = '';
                this.renderGroupConfig(); // 重新渲染以更新操作符和值输入
            }
        });

        $('.condition-operator').on('change', (e) => {
            const index = parseInt(e.target.dataset.index);
            group.conditions[index].operator = e.target.value;
        });

        $('.condition-value').on('input change', (e) => {
            const index = parseInt(e.target.dataset.index);
            group.conditions[index].value = e.target.value;
        });

        // 添加条件
        $('#addConditionBtn').click(() => {
            group.conditions.push({
                field: 'tag',
                operator: '=',
                value: ''
            });
            this.renderGroupConfig();
        });

        // 预览按钮
        $('#previewBtn').click(() => {
            this.previewGroupResults();
        });

        // 重置按钮
        $('#resetBtn').click(() => {
            if (confirm('确定要重置当前规则组的所有条件吗？')) {
                group.conditions = [{ field: 'tag', operator: '=', value: '' }];
                group.orderBy = 'sales_7_days_growth_rate';
                group.orderDirection = 'DESC';
                this.renderGroupConfig();
            }
        });


    }

    addCondition() {
        const group = this.currentRule.groups[this.selectedGroupIndex];
        group.conditions.push({
            field: 'tag',
            operator: '=',
            value: ''
        });
        this.renderGroupConfig();
    }

    removeCondition(index) {
        const group = this.currentRule.groups[this.selectedGroupIndex];
        if (group.conditions.length <= 1) {
            layer.msg('至少需要保留一个筛选条件');
            return;
        }
        group.conditions.splice(index, 1);
        this.renderGroupConfig();
    }

    updateTotalDisplay() {
        const total = this.currentRule.groups.reduce((sum, group) => sum + group.target, 0);
        $('#currentConfigTotal').text(total);

        if (total !== this.currentRule.totalTarget) {
            $('#currentConfigTotal').addClass('text-red-500');
        } else {
            $('#currentConfigTotal').removeClass('text-red-500');
        }
    }

    async previewGroupResults() {
        if (this.selectedGroupIndex < 0) return;

        const group = this.currentRule.groups[this.selectedGroupIndex];
        const anchorId = $('#anchorFilter').val();
        const date = $('#dateFilter').val();

        try {
            const requestData = {
                conditions: group.conditions,
                orderBy: group.orderBy,
                orderDirection: group.orderDirection,
                limit: group.target,
                randomSelection: group.positionConfig.randomSelection
            };

            // 添加主播和日期筛选条件
            if (anchorId && date) {
                requestData.anchor_id = anchorId;
                requestData.date = date;
            }

            const response = await fetch('/api/selection-rules/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.success) {
                this.renderPreviewResults(result.data);

                // 更新预计匹配数量（显示符合条件的总数）
                $('#estimatedMatch').text(result.data.total);

                // 如果启用了随机选取，显示提示
                if (group.positionConfig.randomSelection && result.data.total > group.target) {
                    layer.msg(`从 ${result.data.total} 个符合条件的产品中随机选择了 ${group.target} 个`);
                }

                // 如果实际返回的产品数量少于目标数量，显示提示
                const actualCount = result.data.products ? result.data.products.length : 0;
                if (actualCount < group.target && result.data.total >= group.target) {
                    console.log(`注意：目标数量 ${group.target}，符合条件 ${result.data.total}，实际返回 ${actualCount}`);
                }
            } else {
                layer.msg('预览失败: ' + result.message);
            }
        } catch (error) {
            console.error('预览失败:', error);
            layer.msg('预览失败，请检查网络连接');
        }
    }

    renderPreviewResults(data) {
        const { products } = data;

        // 计算实际显示的产品数量
        const actualCount = products ? products.length : 0;

        // 更新统计数据 - 使用实际产品数量
        $('#previewCount').text(actualCount);
        $('#previewScore').text(actualCount > 0 ? '100%' : '0%');

        // 更新记录数量显示 - 使用实际产品数量
        $('#recordCount').text(`共 ${actualCount} 条记录`);

        // 显示或隐藏复制按钮
        if (actualCount > 0) {
            $('#copyPreviewBtn').show();
        } else {
            $('#copyPreviewBtn').hide();
        }

        // 更新分析数据
        if (products && products.length > 0) {
            // 计算平均价格和佣金
            const avgPrice = products.reduce((sum, item) => sum + (parseFloat(item.product_price) || 0), 0) / products.length;
            const avgCommission = products.reduce((sum, item) => sum + (parseFloat(item.commission_amount) || 0), 0) / products.length;
            
            $('#avgPrice').text(`¥${avgPrice.toFixed(2)}`);
            $('#avgCommission').text(`¥${avgCommission.toFixed(2)}`);
        } else {
            $('#avgPrice').text('¥0');
            $('#avgCommission').text('¥0');
        }

        // 获取表格容器
        const tableBody = $('#previewTableBody');
        
        if (products && products.length > 0) {
            // 生成表格行 - 添加位置、所属组和优先级信息
            const tableRows = products.map((product, index) => {
                // 使用 global_position 作为显示位置，如果没有则使用 index + 1
                const displayPosition = product.global_position || (index + 1);
                const positionClass = product.is_shuffled ? 'text-orange-600' : 'text-blue-600';
                const positionTitle = product.is_shuffled ? '已跨组打乱' : '';

                return `
                <tr>
                        <td class="font-medium ${positionClass}" title="${positionTitle}">${displayPosition}</td>
                    <td>${product.product_id || '--'}</td>
                    <td class="product-name">${product.product_title || '--'}</td>
                    <td class="price">¥${product.product_price || '--'}</td>
                    <td>${product.commission_rate || '--'}%</td>
                    <td class="price">¥${product.commission_amount || '--'}</td>
                    <td>${product.product_source || '--'}</td>
                    <td class="sales">${product.alliance_channel || '--'}</td>
                    <td class="sales">${product.sales_30_days || '--'}</td>
                    <td class="rating">${product.sales_7_days_growth_rate || '--'}${product.sales_7_days_growth_rate ? '%' : ''}</td>
                    <td>${product.tag ? `<span class="tag">${this.getTagName(product.tag)}</span>` : '--'}</td>
                        <td><span class="distribution-item">${product.group_name || '未分组'}</span></td>
                        <td class="font-medium">${product.priority || '--'}</td>
                    <td>${product.anchor_name || '--'}</td>
                    <td>${product.created_at || '--'}</td>
                </tr>
                `;
            }).join('');
            
            tableBody.html(tableRows);

            // 自动滚动到底部
            setTimeout(() => {
                const tableContainer = $('.preview-table-container');
                if (tableContainer.length > 0) {
                    tableContainer.scrollTop(tableContainer[0].scrollHeight);
                }
            }, 100);
        } else {
            // 显示空状态
            tableBody.html(`
                <tr>
                    <td colspan="15" class="text-center text-gray-500 py-8">
                        <i class="fas fa-search text-2xl mb-2 block"></i>
                        没有找到匹配的产品
                        <div class="text-xs text-gray-400 mt-1">请调整筛选条件</div>
                    </td>
                </tr>
            `);
        }
        
        // 更新分布图表数据
        if (typeof updateDistributionData === 'function') {
            updateDistributionData(this.currentRule.groups);
        }
    }

    getTagName(tag) {
        const tagNames = {
            1: '大类目',
            2: '流量品',
            3: '店铺品',
            4: '出单同类品'
        };
        return tagNames[tag] || '未知';
    }

    // 模态框管理
    showSaveModal() {
        $('#saveRuleName').val(this.currentRule.name);
        $('#saveRuleDescription').val(this.currentRule.description);

        // 更新模态框标题，区分创建和更新
        const isUpdate = this.currentRule.id !== null;
        const modalTitle = isUpdate ? '更新选品规则' : '保存选品规则';
        $('#saveRuleModal .modal-header h3').text(modalTitle);

        // 更新确认按钮文本
        const buttonText = isUpdate ? '更新' : '保存';
        $('#confirmSaveBtn').text(buttonText);

        $('#saveRuleModal').removeClass('hidden');
    }

    hideModals() {
        $('#saveRuleModal').addClass('hidden');
    }

    // 创建新规则
    createNewRule() {
        layer.confirm('创建新规则将清空当前配置，是否继续？', {
            icon: 3,
            title: '创建新规则'
        }, (index) => {
            layer.close(index);

            // 重置当前规则配置
            this.currentRule = {
                id: null,  // 清空ID，表示这是新规则
                name: '',
                description: '',
                totalTarget: 500,
                groups: [],
                comprehensiveConfig: {
                    deduplicationEnabled: true,
                    shuffleGroups: []
                }
            };

            this.selectedGroupIndex = -1;

            // 清空界面
            $('#ruleName').val('');
            $('#ruleDescription').val('');
            $('#totalTarget').val(500);
            $('#totalTargetDisplay').text(500);

            // 清空筛选条件
            $('#ruleFilter').val('');
            $('#anchorFilter').val('');
            $('#dateFilter').val('');
            $('#filterWarning').addClass('hidden');

            // 重新渲染界面
            this.renderGroupsList();
            this.updateTotalDisplay();
            this.updateComprehensiveConfig();
            this.renderPreviewResults({ products: [], total: 0 });

            // 显示配置区域的空状态
            $('#ruleConfigArea').html(`
                <div class="empty-state">
                    <i class="fas fa-mouse-pointer"></i>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">开始配置规则</h3>
                    <p class="text-gray-500">请选择左侧的规则组进行配置</p>
                    <p class="text-sm text-gray-400 mt-2">或点击"添加组"创建新的规则组</p>
                </div>
            `);

            layer.msg('已创建新规则，请开始配置');
        });
    }

    // 保存规则
    async saveRule() {
        const name = $('#saveRuleName').val().trim();
        const description = $('#saveRuleDescription').val().trim();

        if (!name) {
            layer.msg('请输入规则名称');
            return;
        }

        if (this.currentRule.groups.length === 0) {
            layer.msg('请至少配置一个规则组');
            return;
        }

        const ruleData = {
            rule_name: name,
            description: description,
            total_target: this.currentRule.totalTarget,
            rule_groups: JSON.stringify(this.currentRule.groups),
            comprehensive_config: JSON.stringify(this.currentRule.comprehensiveConfig)  // 保存综合配置
        };

        try {
            let response;
            const isUpdate = this.currentRule.id !== null;

            if (isUpdate) {
                // 更新现有规则
                response = await fetch(`/api/selection-rules/${this.currentRule.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(ruleData)
                });
            } else {
                // 创建新规则
                response = await fetch('/api/selection-rules', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(ruleData)
                });
            }

            const result = await response.json();

            if (result.success) {
                const message = isUpdate ? '规则更新成功' : '规则保存成功';
                layer.msg(message);
                this.hideModals();

                // 如果是新创建的规则，保存返回的ID
                if (!isUpdate && result.data && result.data.id) {
                    this.currentRule.id = result.data.id;
                }

                this.currentRule.name = name;
                this.currentRule.description = description;
                $('#ruleName').val(name);
                $('#ruleDescription').val(description);

                // 刷新规则列表
                this.loadRulesList();
            } else {
                const message = isUpdate ? '更新失败: ' : '保存失败: ';
                layer.msg(message + result.message);
            }
        } catch (error) {
            console.error('保存失败:', error);
            layer.msg('保存失败，请检查网络连接');
        }
    }

    // 加载已保存的规则
    async loadSavedRules() {
        try {
            const response = await fetch('/api/selection-rules');
            const result = await response.json();

            if (result.success) {
                this.renderSavedRules(result.data);
            } else {
                layer.msg('加载失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载失败:', error);
            layer.msg('加载失败，请检查网络连接');
        }
    }

    renderSavedRules(rules) {
        const container = $('#savedRulesList');
        container.empty();

        if (rules.length === 0) {
            container.html(`
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-folder-open text-3xl mb-3"></i>
                    <p>暂无已保存的规则</p>
                </div>
            `);
            return;
        }

        rules.forEach(rule => {
            const ruleCard = $(`
                <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer transition-colors">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">${rule.rule_name}</h4>
                            <p class="text-sm text-gray-500 mt-1">${rule.description || '无描述'}</p>
                            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                                <span>目标: ${rule.total_target} 个</span>
                                <span>组数: ${JSON.parse(rule.rule_groups).length} 个</span>
                                <span>创建: ${new Date(rule.created_at).toLocaleDateString()}</span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-blue-500 hover:text-blue-700 text-sm" onclick="ruleManager.loadRule(${rule.id})">
                                <i class="fas fa-download mr-1"></i>加载
                            </button>
                            <button class="text-red-500 hover:text-red-700 text-sm" onclick="ruleManager.deleteRule(${rule.id})">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>
            `);
            container.append(ruleCard);
        });
    }

    // 加载选中的规则（新的筛选逻辑）
    async loadSelectedRule() {
        const ruleId = $('#ruleFilter').val();
        const anchorId = $('#anchorFilter').val();
        const date = $('#dateFilter').val();

        // 验证必填项
        if (!ruleId) {
            $('#filterWarning').removeClass('hidden');
            layer.msg('请先选择规则');
            return;
        }

        if (!anchorId || !date) {
            $('#filterWarning').removeClass('hidden');
            layer.msg('请选择主播和日期');
            return;
        }

        $('#filterWarning').addClass('hidden');

        try {
            const response = await fetch(`/api/selection-rules/${ruleId}`);
            const result = await response.json();

            if (result.success) {
                const rule = result.data;

                // 加载规则到当前配置
                this.currentRule = {
                    id: rule.id,  // 设置规则ID，用于后续更新操作
                    name: rule.rule_name,
                    description: rule.description || '',
                    totalTarget: rule.total_target || 500,
                    groups: JSON.parse(rule.rule_groups || '[]'),
                    comprehensiveConfig: JSON.parse(rule.comprehensive_config || '{"deduplicationEnabled": true, "shuffleGroups": []}')
                };

                // 更新界面
                $('#ruleName').val(this.currentRule.name);
                $('#ruleDescription').val(this.currentRule.description);
                $('#totalTarget').val(this.currentRule.totalTarget);
                $('#totalTargetDisplay').text(this.currentRule.totalTarget);

                // 确保字段可编辑（移除可能存在的readonly属性）
                $('#ruleName').prop('readonly', false);
                $('#ruleDescription').prop('readonly', false);
                $('#totalTarget').prop('readonly', false);

                // 渲染规则组
                this.renderGroupsList();
                this.updateTotalDisplay();
                this.updateComprehensiveConfig();

                // 如果有规则组，选中第一个
                if (this.currentRule.groups.length > 0) {
                    this.selectGroup(0);
                }

                layer.msg('规则加载成功');
            } else {
                layer.msg('加载规则失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载规则失败:', error);
            layer.msg('加载失败，请检查网络连接');
        }
    }

    // 加载指定规则
    async loadRule(ruleId) {
        try {
            const response = await fetch(`/api/selection-rules/${ruleId}`);
            const result = await response.json();

            if (result.success) {
                const rule = result.data;
                const groups = JSON.parse(rule.rule_groups);
                
                // 加载综合配置
                let comprehensiveConfig = {
                    deduplicationEnabled: true,
                    shuffleGroups: []
                };
                
                // 如果有保存的综合配置，则使用保存的配置
                if (rule.comprehensive_config) {
                    try {
                        const savedConfig = JSON.parse(rule.comprehensive_config);
                        comprehensiveConfig = { ...comprehensiveConfig, ...savedConfig };
                    } catch (e) {
                        console.warn('解析综合配置失败，使用默认配置:', e);
                    }
                }
                
                // 确保每个组都有完整的位置配置 - 修复兼容性问题
                groups.forEach((group, index) => {
                    // 首先确保基本属性存在
                    if (!group.target) group.target = 50;
                    if (!group.conditions) group.conditions = [{ field: 'tag', operator: '=', value: '' }];
                    if (!group.orderBy) group.orderBy = 'sales_7_days_growth_rate';
                    if (!group.orderDirection) group.orderDirection = 'DESC';
                    
                    // 确保位置配置存在并完整
                    if (!group.positionConfig) {
                        // 计算起始位置
                        let startPos = 1;
                        for (let i = 0; i < index; i++) {
                            const prevGroup = groups[i];
                            if (prevGroup.positionConfig && prevGroup.positionConfig.endPosition) {
                                startPos = prevGroup.positionConfig.endPosition + 1;
                            } else {
                                startPos += prevGroup.target || 50;
                            }
                        }
                        
                        group.positionConfig = {
                            priority: index + 1,
                            startPosition: startPos,
                            endPosition: startPos + (group.target || 50) - 1,
                            randomSelection: false  // 默认不启用随机选取
                        };
                    } else {
                        // 确保现有位置配置的完整性
                        if (typeof group.positionConfig.priority === 'undefined') {
                            group.positionConfig.priority = index + 1;
                        }
                        if (typeof group.positionConfig.startPosition === 'undefined') {
                            group.positionConfig.startPosition = index === 0 ? 1 : this.calculateStartPositionForIndex(index);
                        }
                        if (typeof group.positionConfig.endPosition === 'undefined') {
                            group.positionConfig.endPosition = group.positionConfig.startPosition + (group.target || 50) - 1;
                        }
                        if (typeof group.positionConfig.randomSelection === 'undefined') {
                            group.positionConfig.randomSelection = false;  // 向后兼容
                        }
                    }
                });
                
                this.currentRule = {
                    id: rule.id,  // 设置规则ID，用于后续更新操作
                    name: rule.rule_name,
                    description: rule.description || '',
                    totalTarget: rule.total_target,
                    groups: groups,
                    comprehensiveConfig: comprehensiveConfig  // 使用加载的综合配置
                };

                // 更新界面
                $('#ruleName').val(this.currentRule.name);
                $('#ruleDescription').val(this.currentRule.description);
                $('#totalTarget').val(this.currentRule.totalTarget);
                $('#totalTargetDisplay').text(this.currentRule.totalTarget);

                // 确保字段可编辑（移除可能存在的readonly属性）
                $('#ruleName').prop('readonly', false);
                $('#ruleDescription').prop('readonly', false);
                $('#totalTarget').prop('readonly', false);

                this.selectedGroupIndex = 0;
                this.renderGroupsList();
                this.renderGroupConfig();
                this.updateTotalDisplay();
                this.updateComprehensiveConfig();
                this.hideModals();

                layer.msg('规则加载成功');
            } else {
                layer.msg('加载失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载失败:', error);
            layer.msg('加载失败，请检查网络连接');
        }
    }

    // 删除规则
    async deleteRule(ruleId) {
        layer.confirm('确定要删除这个规则吗？', {
            icon: 3,
            title: '确认删除'
        }, async (index) => {
            try {
                const response = await fetch(`/api/selection-rules/${ruleId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    layer.msg('删除成功');
                    this.loadSavedRules(); // 重新加载列表
                } else {
                    layer.msg('删除失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除失败:', error);
                layer.msg('删除失败，请检查网络连接');
            }
            layer.close(index);
        });
    }

    // 执行选品
    async executeRule() {
        if (!this.currentRule.name) {
            layer.msg('请先设置规则名称');
            return;
        }

        if (this.currentRule.groups.length === 0) {
            layer.msg('请至少配置一个规则组');
            return;
        }

        const total = this.currentRule.groups.reduce((sum, group) => sum + group.target, 0);
        if (total !== this.currentRule.totalTarget) {
            layer.confirm(`当前配置总数量(${total})与目标数量(${this.currentRule.totalTarget})不一致，是否继续执行？`, {
                icon: 3,
                title: '数量不一致'
            }, (index) => {
                layer.close(index);
                this.doExecuteRule();
            });
        } else {
            this.doExecuteRule();
        }
    }

    async doExecuteRule() {
        const loadingIndex = layer.load(1, { content: '正在执行选品...' });

        try {
            const response = await fetch('/api/selection-rules/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    rule_name: this.currentRule.name,
                    description: this.currentRule.description,
                    total_target: this.currentRule.totalTarget,
                    rule_groups: this.currentRule.groups,
                    comprehensiveConfig: this.currentRule.comprehensiveConfig
                })
            });

            const result = await response.json();
            layer.close(loadingIndex);

            if (result.success) {
                let alertMessage = `选品执行成功！<br>
                    总共选中: ${result.data.total_selected} 个产品<br>
                    批次号: ${result.data.batch_number}<br>
                    执行时间: ${new Date(result.data.execution_time).toLocaleString()}`;
                
                if (result.data.shuffleGroups.length > 0) {
                    alertMessage += `<br><span style="color: #ff6b35;">✨ 已启用随机打乱，规则组顺序已随机排列</span>`;
                }

                layer.alert(alertMessage, {
                    icon: 1,
                    title: '执行成功'
                });
                this.loadExecutionHistory(); // 刷新执行历史
            } else {
                layer.msg('执行失败: ' + result.message);
            }
        } catch (error) {
            layer.close(loadingIndex);
            console.error('执行失败:', error);
            layer.msg('执行失败，请检查网络连接');
        }
    }

    // 加载执行历史
    async loadExecutionHistory() {
        try {
            const response = await fetch('/api/selection-rules/history');
            const result = await response.json();

            if (result.success) {
                this.renderExecutionHistory(result.data);
            } else {
                console.error('加载执行历史失败:', result.message);
                // 如果是数据库相关错误，显示空状态而不是错误消息
                if (result.message && result.message.includes('表') || result.message.includes('table')) {
                    this.renderExecutionHistory([]);
                } else {
                    // 对于其他错误，显示空状态
                    this.renderExecutionHistory([]);
                }
            }
        } catch (error) {
            console.error('加载执行历史失败:', error);
            // 网络错误或其他错误，显示空状态
            this.renderExecutionHistory([]);
        }
    }

    renderExecutionHistory(executions) {
        const tbody = $('#executionHistoryTable');
        tbody.empty();

        if (executions.length === 0) {
            tbody.html(`
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                        <i class="fas fa-history text-2xl mb-2"></i>
                        <p>暂无执行历史</p>
                    </td>
                </tr>
            `);
            return;
        }

        executions.forEach(execution => {
            const statusClass = execution.status === 'completed' ? 'text-green-600' :
                               execution.status === 'failed' ? 'text-red-600' : 'text-yellow-600';
            const statusText = execution.status === 'completed' ? '成功' :
                              execution.status === 'failed' ? '失败' : '部分成功';

            const row = $(`
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${new Date(execution.execution_time).toLocaleString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${execution.rule_name}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${execution.total_selected}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${execution.batch_number || '--'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm ${statusClass}">
                        ${statusText}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="ruleManager.viewExecutionDetail(${execution.id})">
                            <i class="fas fa-eye mr-1"></i>详情
                        </button>
                        ${execution.batch_number ? `
                            <a href="/api/products/export?batch_number=${execution.batch_number}" class="text-green-600 hover:text-green-900">
                                <i class="fas fa-download mr-1"></i>导出
                            </a>
                        ` : ''}
                    </td>
                </tr>
            `);
            tbody.append(row);
        });
    }

    // 查看执行详情
    async viewExecutionDetail(executionId) {
        try {
            const response = await fetch(`/api/selection-rules/history/${executionId}`);
            const result = await response.json();

            if (result.success) {
                const execution = result.data;
                const executionResult = JSON.parse(execution.execution_result || '{}');

                let detailHtml = `
                    <div class="space-y-4">
                        <div><strong>规则名称:</strong> ${execution.rule_name}</div>
                        <div><strong>执行时间:</strong> ${new Date(execution.execution_time).toLocaleString()}</div>
                        <div><strong>总选中数量:</strong> ${execution.total_selected}</div>
                        <div><strong>批次号:</strong> ${execution.batch_number || '--'}</div>
                        <div><strong>状态:</strong> ${execution.status}</div>
                `;

                if (executionResult.groups) {
                    detailHtml += '<div><strong>各组执行结果:</strong></div>';
                    detailHtml += '<div class="ml-4 space-y-2">';
                    executionResult.groups.forEach(group => {
                        detailHtml += `<div>• ${group.name}: 目标 ${group.target} 个，实际选中 ${group.selected} 个</div>`;
                    });
                    detailHtml += '</div>';
                }

                if (execution.error_message) {
                    detailHtml += `<div class="text-red-600"><strong>错误信息:</strong> ${execution.error_message}</div>`;
                }

                detailHtml += '</div>';

                layer.open({
                    type: 1,
                    title: '执行详情',
                    content: detailHtml,
                    area: ['600px', '400px']
                });
            } else {
                layer.msg('加载详情失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载详情失败:', error);
            layer.msg('加载详情失败，请检查网络连接');
        }
    }

    renderShuffleGroupOptions() {
        // 创建带有位置配置的组数组副本，然后按优先级排序
        const sortedGroups = this.currentRule.groups.map((group, index) => {
            // 确保组具有完整的位置配置
            const ensuredGroup = this.ensureGroupPositionConfig(group, index);
            return {
                group: ensuredGroup,
                originalIndex: index
            };
        }).sort((a, b) => {
            // 按优先级升序排序（优先级小的在前）
            const priorityA = a.group.positionConfig?.priority || 999;
            const priorityB = b.group.positionConfig?.priority || 999;
            return priorityA - priorityB;
        });

        return sortedGroups.map(({ group, originalIndex }) => {
            const isChecked = this.currentRule.comprehensiveConfig.shuffleGroups.includes(originalIndex);
            return `
                <label class="flex items-center">
                    <input type="checkbox" id="shuffleGroup${originalIndex}" class="mr-2" ${isChecked ? 'checked' : ''}>
                    <span class="text-sm text-gray-700">${group.name} (优先级: ${group.positionConfig.priority})</span>
                </label>
            `;
        }).join('');
    }

    bindShuffleGroupEvents() {
        // 移除之前的事件监听器，避免重复绑定
        $('#shuffleGroupsSelection').off('change', 'input[type="checkbox"]');
        
        // 添加事件监听器
        $('#shuffleGroupsSelection').on('change', 'input[type="checkbox"]', (e) => {
            const isChecked = e.target.checked;
            const groupIndex = parseInt(e.target.id.replace('shuffleGroup', ''));
            
            if (isChecked) {
                // 添加到选中列表
                if (!this.currentRule.comprehensiveConfig.shuffleGroups.includes(groupIndex)) {
                    this.currentRule.comprehensiveConfig.shuffleGroups.push(groupIndex);
                }
            } else {
                // 从选中列表移除
                this.currentRule.comprehensiveConfig.shuffleGroups = this.currentRule.comprehensiveConfig.shuffleGroups.filter(g => g !== groupIndex);
            }
            
            // 验证至少选择2个规则组
            if (this.currentRule.comprehensiveConfig.shuffleGroups.length < 2) {
                layer.msg('至少需要选择2个规则组进行跨组打乱');
                
                // 如果少于2个，自动取消随机打乱功能
                if (this.currentRule.comprehensiveConfig.shuffleGroups.length === 0) {
                    $('#shuffleGroups').prop('checked', false);
                    $('#shuffleGroupsSelection').addClass('hidden');
                }
            }
        });
    }
    
    // 复制预览结果
    copyPreviewResults() {
        const tableBody = $('#previewTableBody');
        const rows = tableBody.find('tr');
        
        if (rows.length === 0 || (rows.length === 1 && rows.first().find('td').length === 1)) {
            layer.msg('暂无数据可复制');
            return;
        }
        
        let copyText = '';
        let index = 1;
        
        rows.each(function() {
            const row = $(this);
            const cells = row.find('td');
            
            // 跳过空状态行
            if (cells.length === 1) {
                return;
            }
            
            // 提取每行的数据
            const productId = cells.eq(1).text().trim();
          
            copyText += `${productId}\n`;
           
            
            index++;
        });
        
        // 复制到剪贴板
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代Clipboard API
            navigator.clipboard.writeText(copyText).then(() => {
                layer.msg(`已复制 ${index - 1} 条产品数据到剪贴板`);
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyText(copyText, index - 1);
            });
        } else {
            // 降级方案
            this.fallbackCopyText(copyText, index - 1);
        }
    }
    
    // 降级复制方案
    fallbackCopyText(text, count) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                layer.msg(`已复制 ${count} 条产品数据到剪贴板`);
            } else {
                layer.msg('复制失败，请手动复制');
            }
        } catch (err) {
            console.error('复制失败:', err);
            layer.msg('复制失败，请手动复制');
        } finally {
            document.body.removeChild(textArea);
        }
    }

    // 初始化筛选模块
    initFilterModule() {
        this.loadRulesList();
        this.loadAnchorsList();
        this.setDefaultDate();
    }

    // 加载规则列表
    async loadRulesList() {
        try {
            const response = await fetch('/api/selection-rules');
            const result = await response.json();

            if (result.success) {
                const ruleSelect = $('#ruleFilter');
                ruleSelect.empty().append('<option value="">请选择规则</option>');

                result.data.forEach(rule => {
                    ruleSelect.append(`<option value="${rule.id}">${rule.rule_name}</option>`);
                });
            }
        } catch (error) {
            console.error('加载规则列表失败:', error);
        }
    }

    // 加载主播列表
    async loadAnchorsList() {
        try {
            const response = await fetch('/api/anchors?mode=full');
            const result = await response.json();

            if (result.anchors) {
                const anchorSelect = $('#anchorFilter');
                anchorSelect.empty().append('<option value="">请选择主播</option>');

                result.anchors.forEach(anchor => {
                    anchorSelect.append(`<option value="${anchor.id}">${anchor.anchor_name}</option>`);
                });
            }
        } catch (error) {
            console.error('加载主播列表失败:', error);
        }
    }

    // 设置默认日期为今天
    setDefaultDate() {
        const today = new Date().toISOString().split('T')[0];
        $('#dateFilter').val(today);
    }


}


let ruleManager;
$(document).ready(() => {
    ruleManager = new SelectionRuleManager();

    // 在控制台提示用户可以运行测试
    console.log('淘宝热销产品选品规则系统已加载');
    console.log('运行 previewAllAPI() 来测试预览全部API功能');
    console.log('运行 testPreviewData() 来测试前端预览功能');

    // 添加测试前端预览功能
    window.testPreviewData = function() {
        const testData = {
            products: [
                {
                    product_id: 'TB001',
                    product_title: '测试产品1 - 高品质商品',
                    product_price: '99.99',
                    commission_rate: '5.5',
                    commission_amount: '5.50',
                    product_source: '淘宝搜索',
                    alliance_channel: '直播严选',
                    sales_30_days: '1200',
                    sales_7_days_growth_rate: '15.5',
                    tag: 1,
                    group_name: '大类目组',
                    priority: 1,
                    anchor_name: '测试主播',
                    created_at: '2024-01-15',
                    global_position: 1
                },
                {
                    product_id: 'TB002',
                    product_title: '测试产品2 - 流量爆款',
                    product_price: '59.99',
                    commission_rate: '8.0',
                    commission_amount: '4.80',
                    product_source: '热浪联盟',
                    alliance_channel: '天天热卖',
                    sales_30_days: '2500',
                    sales_7_days_growth_rate: '25.8',
                    tag: 2,
                    group_name: '流量品组',
                    priority: 2,
                    anchor_name: '测试主播2',
                    created_at: '2024-01-16',
                    global_position: 2
                },
                {
                    product_id: 'TB003',
                    product_title: '测试产品3 - 店铺精选',
                    product_price: '129.99',
                    commission_rate: '6.5',
                    commission_amount: '8.45',
                    product_source: '淘宝直播',
                    alliance_channel: '源头优选',
                    sales_30_days: '800',
                    sales_7_days_growth_rate: '12.3',
                    tag: 3,
                    group_name: '店铺品组',
                    priority: 3,
                    anchor_name: '测试主播3',
                    created_at: '2024-01-17',
                    global_position: 3
                }
            ],
            total: 3
        };

        console.log('测试前端预览功能...');
        ruleManager.renderPreviewResults(testData);
        console.log('✅ 测试数据已加载到产品列表');
    };

    // 测试预览全部API功能
    window.previewAllAPI = async function() {
        console.log('测试预览全部API功能...');
        
        // 创建测试数据
        const testGroups = [
            {
                name: '测试组1',
                target: 10,
                conditions: [
                    { field: 'tag', operator: '=', value: '1' }
                ],
                orderBy: 'sales_7_days_growth_rate',
                orderDirection: 'DESC',
                positionConfig: {
                    priority: 1,
                    startPosition: 1,
                    endPosition: 10,
                    allowOverlap: false,
                    randomSelection: false
                }
            },
            {
                name: '测试组2',
                target: 15,
                conditions: [
                    { field: 'tag', operator: '=', value: '2' }
                ],
                orderBy: 'commission_amount',
                orderDirection: 'DESC',
                positionConfig: {
                    priority: 2,
                    startPosition: 11,
                    endPosition: 25,
                    allowOverlap: false,
                    randomSelection: true  // 启用随机选取
                }
            }
        ];
        
        const testConfig = {
            deduplicationEnabled: true
        };
        
        try {
            const response = await fetch('/api/selection-rules/preview-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    groups: testGroups,
                    comprehensiveConfig: testConfig,
                    totalTarget: 500
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 预览全部API测试成功');
                console.log('返回数据:', result.data);
                
                // 检查随机选取组
                const randomGroup = result.data.groups.find(g => g.randomSelection);
                if (randomGroup) {
                    console.log('✅ 随机选取组测试成功:', randomGroup.name);
                } else {
                    console.log('⚠️ 没有找到随机选取组');
                }
            } else {
                console.log('❌ 预览全部API测试失败:', result.message);
            }
        } catch (error) {
            console.error('❌ 预览全部API测试出错:', error);
        }
    };
});

// 在控制台提示用户可以运行测试
console.log('淘宝热销产品选品规则系统已加载');
// console.log('运行 previewAllAPI() 来测试预览全部API功能');