import crypto from 'crypto';
import fetch from 'node-fetch';
import { database } from './database.js';
import { extractH5Token, updateAnchorCookie, updateAnchorCookieToDatabase } from './utils/cookie-utils.js';
import { handleTaobaoApiResponse } from './utils/response-handler.js';

/**
 * 为单个主播更新Cookie
 * @param {object} anchor - 主播信息
 * @returns {Promise<object>} - 更新结果
 */
export async function updateAnchorCookieFromAPI(anchor) {
  try {
    console.log(`🔄 [${anchor.anchor_name}] 开始更新Cookie...`);

    // 调用API获取新的cookie
    const cookieResult = await fetchCookieFromTaobaoAPI(anchor);

    if (!cookieResult.success) {
      return {
        success: false,
        error: cookieResult.error || '获取Cookie失败'
      };
    }

    if (!cookieResult.hasNewCookies) {
      console.log(`⚪ [${anchor.anchor_name}] 无新Cookie需要更新`);
      return {
        success: true,
        message: '无新Cookie需要更新',
        noUpdate: true  // 标记为无需更新
      };
    }

    // 使用响应处理器返回的更新后的Cookie
    const updatedCookie = cookieResult.newCookie || updateAnchorCookie(
      anchor.anchor_name,
      cookieResult.cookies,
      anchor.anchor_cookie
    );

    // 保存到数据库
    const dbUpdateResult = await updateAnchorCookieToDatabase(anchor.id, updatedCookie, anchor.anchor_name);

    if (dbUpdateResult.success) {
      console.log(`✅ [${anchor.anchor_name}] Cookie更新成功`);
      return {
        success: true,
        message: 'Cookie更新成功',
        newCookies: cookieResult.cookies,
        updatedCookie: updatedCookie
      };
    } else {
      return {
        success: false,
        error: dbUpdateResult.error || '数据库更新失败'
      };
    }

  } catch (error) {
    console.error(`[${anchor.anchor_name}] Cookie更新异常:`, error);
    return {
      success: false,
      error: error.message || '未知错误'
    };
  }
}

/**
 * 调用淘宝API获取新的Cookie
 * @param {object} anchor - 主播信息
 * @returns {Promise<object>} - API调用结果
 */
export async function fetchCookieFromTaobaoAPI(anchor) {
  try {
    console.log(`[${anchor.anchor_name}] 开始调用淘宝API获取新Cookie...`);

    // 从主播Cookie中提取h5Token
    const h5Token = extractH5Token(anchor.anchor_cookie);
    if (!h5Token) {
      return {
        success: false,
        error: '无法从主播Cookie中提取h5Token'
      };
    }

    // API配置
    const appKey = '12574478';
    const api = 'mtop.taobao.dreamweb.anchor.homepage.async.get';
    const version = '1.0';
    
    // 请求数据
    const data = {
      version: 1,
      api: "anchorInfo"
    };

    // 生成时间戳和签名
    const timestamp = Date.now();
    const dataStr = JSON.stringify(data);
    const signString = `${h5Token}&${timestamp}&${appKey}&${dataStr}`;
    
    // 使用MD5生成签名
    const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex');

    // 构建请求参数
    const params = new URLSearchParams({
      jsv: '2.7.4',
      appKey: appKey,
      t: timestamp.toString(),
      sign: sign,
      api: api,
      v: version,
      preventFallback: 'true',
      type: 'json',
      dataType: 'json',
      data: JSON.stringify(data)
    });

    // 构建请求URL
    const requestUrl = `https://h5api.m.taobao.com/h5/${api}/${version}/?${params.toString()}`;

    console.log(`[${anchor.anchor_name}] 请求URL: ${requestUrl.substring(0, 100)}...`);
    console.log(`[${anchor.anchor_name}] 使用h5Token: ${h5Token.substring(0, 20)}...`);

    // 设置请求头，参考直播计划同步的格式
    const headers = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Referer': 'https://liveplatform.taobao.com/restful/index/live/keypoint',
      'Cookie': anchor.anchor_cookie,
      'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Ch-Ua-Platform': '"Windows"',
      'Sec-Fetch-Dest': 'script',
      'Sec-Fetch-Mode': 'no-cors',
      'Sec-Fetch-Site': 'same-site'
    };

    // 发起请求
    const response = await fetch(requestUrl, {
      method: 'GET',
      headers: headers
    });

    console.log(`[${anchor.anchor_name}] API响应状态: ${response.status}`);

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP错误: ${response.status} ${response.statusText}`
      };
    }

    // 使用统一的响应处理器
    const responseResult = await handleTaobaoApiResponse(response, anchor.anchor_name, anchor.anchor_cookie);

    if (responseResult.cookieUpdated) {
      console.log(`[${anchor.anchor_name}] 响应处理器检测到新Cookie`);
      return {
        success: true,
        hasNewCookies: true,
        cookies: responseResult.cookies,
        newCookie: responseResult.newCookie
      };
    } else {
      console.log(`[${anchor.anchor_name}] 响应中无新Cookie`);
      return {
        success: true,
        hasNewCookies: false,
        cookies: {}
      };
    }

  } catch (error) {
    console.error(`[${anchor.anchor_name}] API请求失败:`, error);
    return {
      success: false,
      error: error.message || '网络请求失败'
    };
  }
}

/**
 * 批量更新所有主播的Cookie
 * @returns {Promise<object>} - 批量更新结果
 */
export async function batchUpdateAllAnchorsCookies() {
  try {
    console.log('🚀 开始批量更新所有主播Cookie...');

    // 获取所有活跃主播
    const anchorsResult = await database.all(
      "SELECT id, anchor_name, anchor_id, anchor_cookie FROM anchors WHERE status = 'active' ORDER BY anchor_name ASC"
    );

    if (!anchorsResult.results || anchorsResult.results.length === 0) {
      return {
        success: true,
        message: '没有找到活跃的主播',
        totalAnchors: 0,
        updatedCount: 0,
        noUpdateCount: 0,
        failedCount: 0,
        results: []
      };
    }

    const anchors = anchorsResult.results;
    console.log(`📊 找到 ${anchors.length} 个活跃主播，开始逐个更新...`);

    const results = [];
    let updatedCount = 0;
    let noUpdateCount = 0;
    let failedCount = 0;

    // 逐个更新主播Cookie
    for (const anchor of anchors) {
      const result = await updateAnchorCookieFromAPI(anchor);
      
      if (result.success) {
        if (result.noUpdate) {
          noUpdateCount++;
        } else {
          updatedCount++;
        }
      } else {
        failedCount++;
      }

      results.push({
        anchorName: anchor.anchor_name,
        anchorId: anchor.anchor_id,
        ...result
      });

      // 添加延迟避免请求过于频繁
      // await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log(`✅ 批量更新完成: 总计${anchors.length}个, 更新${updatedCount}个, 无需更新${noUpdateCount}个, 失败${failedCount}个`);

    return {
      success: true,
      message: '批量更新完成',
      totalAnchors: anchors.length,
      updatedCount,
      noUpdateCount,
      failedCount,
      results
    };

  } catch (error) {
    console.error('批量更新Cookie失败:', error);
    return {
      success: false,
      error: error.message || '批量更新失败',
      totalAnchors: 0,
      updatedCount: 0,
      noUpdateCount: 0,
      failedCount: 0,
      results: []
    };
  }
}
