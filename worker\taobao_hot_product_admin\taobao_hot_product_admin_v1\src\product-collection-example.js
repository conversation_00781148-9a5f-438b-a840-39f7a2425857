import ProductCollection from './product-collection.js';

/**
 * 商品采集使用示例
 */
async function example() {
    const collector = new ProductCollection();

    try {
        // 示例1: 采集单页商品数据并保存到数据库
        console.log('=== 示例1: 采集单页商品数据并保存 ===');
        
        const anchorId = 'your_anchor_id'; // 替换为实际的主播ID
        const filters = {
            code: "1001" // 替换为实际的过滤条件
        };
        
        const singlePageResult = await collector.collectAndSaveProducts(
            anchorId, 
            filters, 
            1,    // 页码
            30,   // 每页数量
            true  // 保存到数据库
        );
        
        if (singlePageResult.success) {
            console.log('采集成功!');
            console.log(`主播: ${singlePageResult.anchor.name}`);
            console.log(`采集商品数量: ${singlePageResult.data.length}`);
            if (singlePageResult.saveResult) {
                console.log(`保存到数据库: ${singlePageResult.saveResult.saved} 个商品`);
            }
        } else {
            console.error('采集失败:', singlePageResult.error);
        }

        // 示例2: 批量采集多页商品数据并保存
        console.log('\n=== 示例2: 批量采集多页商品数据并保存 ===');
        
        const multiPageResult = await collector.collectMultiplePagesAndSave(
            anchorId,
            filters,
            3,   // 最大页数
            30   // 每页数量
        );
        
        if (multiPageResult.success) {
            console.log('批量采集成功!');
            console.log(`总页数: ${multiPageResult.totalPages}`);
            console.log(`总商品数量: ${multiPageResult.totalProducts}`);
            console.log(`保存到数据库: ${multiPageResult.totalSaved} 个商品`);
        } else {
            console.error('批量采集失败:', multiPageResult.error);
        }

        // 示例3: 只采集不保存（用于预览）
        console.log('\n=== 示例3: 只采集不保存（预览模式） ===');
        
        const previewResult = await collector.collectAndSaveProducts(
            anchorId,
            filters,
            1,     // 页码
            10,    // 每页数量
            false  // 不保存到数据库
        );
        
        if (previewResult.success) {
            console.log('预览采集成功!');
            console.log(`采集商品数量: ${previewResult.data.length}`);
            console.log('商品列表:');
            previewResult.data.forEach((product, index) => {
                console.log(`${index + 1}. ${product.itemName} - ¥${product.itemPrice}`);
            });
        } else {
            console.error('预览采集失败:', previewResult.error);
        }

        // 示例4: 获取所有活跃主播并批量采集
        console.log('\n=== 示例4: 获取所有活跃主播并批量采集 ===');
        
        const anchors = await collector.getAllActiveAnchors();
        console.log(`找到 ${anchors.length} 个活跃主播`);
        
        for (const anchor of anchors.slice(0, 2)) { // 只处理前2个主播作为示例
            console.log(`\n正在采集主播: ${anchor.anchor_name}`);
            
            const result = await collector.collectAndSaveProducts(
                anchor.anchor_id,
                filters,
                1,    // 页码
                20,   // 每页数量
                true  // 保存到数据库
            );
            
            if (result.success) {
                console.log(`${anchor.anchor_name} 采集成功: ${result.data.length} 个商品`);
                if (result.saveResult) {
                    console.log(`保存: ${result.saveResult.saved} 个商品`);
                }
            } else {
                console.error(`${anchor.anchor_name} 采集失败:`, result.error);
            }
            
            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

    } catch (error) {
        console.error('示例执行失败:', error);
    }
}

// 如果直接运行此文件，则执行示例
if (import.meta.url === `file://${process.argv[1]}`) {
    example().catch(console.error);
}

export default example;
