{"api": "mtop.taobao.cic.downlink.newstation.itemcenter.item.conditions", "data": {"data": [{"filterKey": "source", "filterList": [{"key": "shopType", "multiple": true, "optionList": [{"name": "全部", "value": ""}, {"name": "天猫", "value": "天猫店"}, {"name": "淘宝", "value": "淘宝店"}], "title": "商品来源", "type": "button"}], "filterTitle": "店铺类型"}, {"filterKey": "classification", "filterList": [{"key": "cateIds", "multiple": true, "optionList": [{"code": "0", "name": "全部", "rankThresholdAnchor": 0, "rankThresholdSale": 0, "value": ""}, {"child": [{"name": "全部", "value": ""}, {"name": "美妆香水", "value": "50010788"}, {"name": "美容美体仪器", "value": "126762001"}], "code": "1", "group": "1", "multiple": true, "name": "美容美妆", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50010788,126762001"}, {"child": [{"name": "全部", "value": ""}, {"name": "美发护发", "value": "50023282"}, {"name": "面部护肤/身体护理", "value": "1801"}, {"name": "家庭/个人清洁工具", "value": "50016348"}, {"name": "隐形眼镜/护理液", "value": "50023722"}, {"name": "清洁剂/卫生巾", "value": "50025705"}], "code": "2", "group": "1", "multiple": true, "name": "个护清洁", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50023282,1801,50016348,50023722,50025705"}, {"child": [{"name": "全部", "value": ""}, {"name": "孕产用品", "value": "50022517"}, {"name": "婴童用品", "value": "50014812"}, {"name": "婴童尿裤", "value": "201207402"}, {"name": "奶粉辅食", "value": "35"}, {"name": "文教用品", "value": "50018004"}], "code": "3", "group": "1", "multiple": true, "name": "母婴用品", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50022517,50014812,201207402,35,50018004"}, {"child": [{"name": "全部", "value": ""}, {"name": "童装", "value": "50008165"}, {"name": "童鞋", "value": "122650005"}], "code": "4", "group": "1", "multiple": true, "name": "童装童鞋", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50008165,122650005"}, {"child": [{"name": "全部", "value": ""}, {"name": "女装", "value": "16"}, {"name": "内衣", "value": "1625"}, {"name": "饰品", "value": "50013864"}, {"name": "女鞋", "value": "50006843"}, {"name": "男装", "value": "30"}, {"name": "配饰", "value": "50010404"}, {"name": "男鞋", "value": "50011740"}], "code": "5", "group": "2", "multiple": true, "name": "服饰", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "16,1625,50013864,50006843,30,50010404,50011740"}, {"child": [{"name": "全部", "value": ""}, {"name": "手表", "value": "50468001"}, {"name": "眼镜", "value": "28"}], "code": "6", "group": "2", "multiple": true, "name": "手表眼镜", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50468001,28"}, {"child": [{"name": "全部", "value": ""}, {"name": "箱包皮具", "value": "50006842"}], "code": "7", "group": "2", "multiple": true, "name": "箱包皮具", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50006842"}, {"child": [{"name": "全部", "value": ""}, {"name": "珠宝", "value": "50011397"}], "code": "8", "group": "2", "multiple": true, "name": "珠宝", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50011397"}, {"child": [{"name": "全部", "value": ""}, {"name": "水产肉类/新鲜蔬果/熟食", "value": "50050359"}], "code": "9", "group": "3", "multiple": true, "name": "生鲜", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50050359"}, {"child": [{"name": "全部", "value": ""}, {"name": "粮油调味/速食/干货/烘焙", "value": "50016422"}], "code": "10", "group": "3", "multiple": true, "name": "粮油副食", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50016422"}, {"child": [{"name": "全部", "value": ""}, {"name": "茶类", "value": "124458005"}, {"name": "咖啡冲饮", "value": "50026316"}, {"name": "酒类", "value": "50008141"}, {"name": "零食坚果", "value": "50002766"}], "code": "11", "group": "3", "multiple": true, "name": "零食饮品", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "124458005,50026316,50008141,50002766"}, {"child": [{"name": "全部", "value": ""}, {"name": "保健品", "value": "50026800"}, {"name": "营养滋补品", "value": "50020275"}], "code": "12", "group": "3", "multiple": true, "name": "营养保健", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50026800,50020275"}, {"child": [{"name": "全部", "value": ""}, {"name": "3C数码配件", "value": "50008090"}, {"name": "存储设备", "value": "50012164"}, {"name": "电脑配件", "value": "11"}, {"name": "智能设备", "value": "124242008"}, {"name": "乐器", "value": "50017300"}, {"name": "办公设备", "value": "50007218"}, {"name": "电玩游戏", "value": "20"}], "code": "13", "group": "4", "multiple": true, "name": "3C数码", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50008090,50012164,11,124242008,50017300,50007218,20"}, {"child": [{"name": "全部", "value": ""}, {"name": "大家电", "value": "50022703"}], "code": "14", "group": "4", "multiple": true, "name": "大家电", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50022703"}, {"child": [{"name": "全部", "value": ""}, {"name": "生活电器", "value": "50012100"}, {"name": "护理按摩器材", "value": "50002768"}, {"name": "厨房电器", "value": "50012082"}, {"name": "影音电器", "value": "50011972"}], "code": "15", "group": "4", "multiple": true, "name": "生活电器", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50012100,50002768,50012082,50011972"}, {"child": [{"name": "全部", "value": ""}, {"name": "居家布艺", "value": "122852001"}, {"name": "居家日用", "value": "21"}, {"name": "床上用品", "value": "50008163"}, {"name": "收纳整理", "value": "122928002"}, {"name": "家居饰品", "value": "50020808"}, {"name": "设计定制服务", "value": "50025004"}, {"name": "特色手工艺", "value": "50020857"}, {"name": "节庆用品", "value": "122950001"}], "code": "16", "group": "5", "multiple": true, "name": "家居百货", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "122852001,21,50008163,122928002,50020808,50025004,50020857,122950001"}, {"child": [{"name": "全部", "value": ""}, {"name": "餐饮具", "value": "122952001"}, {"name": "厨房用具", "value": "50016349"}], "code": "17", "group": "5", "multiple": true, "name": "厨房用具", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "122952001,50016349"}, {"child": [{"name": "全部", "value": ""}, {"name": "住宅家具", "value": "50008164"}, {"name": "家装主材", "value": "27"}, {"name": "家装灯饰", "value": "126700003"}, {"name": "基础建材", "value": "50020332"}, {"name": "五金工具", "value": "50020485"}, {"name": "电子/电工", "value": "50020579"}, {"name": "装修设计", "value": "50023804"}, {"name": "商业办公家具", "value": "50020611"}, {"name": "全屋定制", "value": "124050001"}], "code": "18", "group": "5", "multiple": true, "name": "家装", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50008164,27,126700003,50020332,50020485,50023804,50020611,50020579,124050001"}, {"child": [{"name": "全部", "value": ""}, {"name": "运动休闲服", "value": "50011699"}, {"name": "运动鞋", "value": "50012029"}], "code": "19", "group": "6", "multiple": true, "name": "运动服鞋", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50011699,50012029"}, {"child": [{"name": "全部", "value": ""}, {"name": "户外用品", "value": "50013886"}, {"name": "自行车", "value": "122684003"}, {"name": "运动健身", "value": "50010728"}, {"name": "运动包及配件", "value": "50510002"}], "code": "20", "group": "6", "multiple": true, "name": "户外装备", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50013886,122684003,50010728,50510002"}, {"child": [{"name": "全部", "value": ""}, {"name": "汽车零部件/美容维保", "value": "201162107"}, {"name": "车用清洗用品/清洗工具", "value": "26"}, {"name": "电动车/配件", "value": "124354002"}, {"name": "摩托车/装备配件", "value": "50074001"}], "code": "21", "group": "7", "multiple": true, "name": "汽车", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "201162107,26,124354002,50074001"}, {"child": [{"name": "全部", "value": ""}, {"name": "玩具/益智", "value": "25"}, {"name": "模玩/动漫/娃圈三坑/桌游", "value": "124484008"}], "code": "22", "group": "7", "multiple": true, "name": "玩具", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "25,124484008"}, {"child": [{"name": "全部", "value": ""}, {"name": "鲜花速递/花卉仿真/绿植园艺", "value": "50007216"}], "code": "23", "group": "7", "multiple": true, "name": "鲜花园艺", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50007216"}, {"child": [{"name": "全部", "value": ""}, {"name": "宠物/食品用品", "value": "29"}], "code": "24", "group": "7", "multiple": true, "name": "宠物", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "29"}, {"child": [{"name": "全部", "value": ""}, {"name": "本地化生活服务", "value": "50025111"}, {"name": "餐饮美食卡券", "value": "50008075"}, {"name": "电影/演出/体育赛事", "value": "50025110"}, {"name": "兑换卡", "value": "201173506"}], "code": "25", "group": "8", "multiple": true, "name": "本地生活", "rankThresholdAnchor": 1000000000, "rankThresholdSale": 100000000, "value": "50025111,50008075,50025110,201173506"}, {"child": [{"name": "全部", "value": ""}, {"name": "书籍杂志", "value": "33"}, {"name": "音乐影视", "value": "34"}, {"name": "文玩字画", "value": "23"}], "code": "26", "group": "8", "multiple": true, "name": "图画音像", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "33,34,23"}, {"child": [{"name": "全部", "value": ""}, {"name": "教育培训", "value": "50014927"}], "code": "27", "group": "8", "multiple": true, "name": "教育培训", "rankThresholdAnchor": 1000000000, "rankThresholdSale": 100000000, "value": "50014927"}, {"child": [{"name": "全部", "value": ""}, {"name": "保健用品", "value": "50023717"}, {"name": "医疗器械", "value": "50023721"}], "code": "28", "group": "8", "multiple": true, "name": "医疗健康", "rankThresholdAnchor": 500, "rankThresholdSale": 2000, "value": "50023717,50023721"}], "title": "商品分类", "type": "button"}], "filterTitle": "商品分类"}, {"filterKey": "itemInfo", "filterList": [{"key": "priceSelect", "optionList": [{"name": "", "position": "prefix", "unit": "￥"}, {"position": "prefix", "unit": "￥"}], "title": "价格区间", "type": "input"}, {"desc": "商品近一年的直播销量件数，未剔除发生退货或退款的销量数据", "key": "soldQuantity365Select", "optionList": [{"position": "prefix", "unit": "最低"}, {"position": "prefix", "unit": "最高"}], "title": "365天直播销量", "type": "input"}, {"key": "shopLevel", "multiple": true, "optionList": [{"name": "全部", "value": ""}, {"click": "shopType:天猫店", "name": "5星", "value": "5星级"}, {"click": "shopType:天猫店", "name": "4星", "value": "4星级"}, {"click": "shopType:天猫店", "name": "3星", "value": "3星级"}, {"click": "shopType:天猫店", "name": "2星", "value": "2星级"}, {"click": "shopType:天猫店", "name": "1星", "value": "1星级"}, {"click": "shopType:淘宝店", "name": "金冠级", "pic": "https://gw.alicdn.com/tps/i1/TB1BwL9HpXXXXaZXFXXCBGNFFXX-24-24.png", "value": "1红冠,2红冠,3红冠,4红冠,5红冠"}, {"click": "shopType:淘宝店", "name": "皇冠级", "pic": "https://gw.alicdn.com/tps/i4/TB1wA25HpXXXXcwXVXXCBGNFFXX-24-24.png", "value": "1皇冠,2皇冠,3皇冠,4皇冠,5皇冠"}, {"click": "shopType:淘宝店", "name": "钻级", "pic": "https://gw.alicdn.com/tps/i3/TB1c9z_HpXXXXcQXpXXCBGNFFXX-24-24.png", "value": "1钻,2钻,3钻,4钻,5钻"}, {"click": "shopType:淘宝店", "name": "心级", "pic": "https://gw.alicdn.com/tps/i2/TB1T8_4HpXXXXagaXXXCBGNFFXX-24-24.png", "value": "1心,2心,3心,4心,5心"}], "title": "所属店铺星级", "type": "select"}, {"desc": "商家设置了样品服务的商品", "key": "hasSample", "optionList": [{"value": "1"}, {"value": ""}], "title": "有样品", "type": "checkbox"}], "filterTitle": "商品信息"}, {"filterKey": "commision", "filterList": [{"key": "commissionRateSelect", "optionList": [{"position": "suffix", "unit": "%"}, {"position": "suffix", "unit": "%"}], "title": "佣金率", "type": "input"}, {"desc": "本账号所有带着生效定向佣金计划的商品", "key": "isDirect", "optionList": [{"value": "1"}, {"value": ""}], "title": "专属给我", "type": "checkbox"}], "filterTitle": "商品佣金"}, {"filterKey": "featured", "filterList": [{"key": "featured", "multiple": true, "optionList": [{"name": "全部", "value": ""}, {"desc": "官方推荐爆款好价", "name": "爆品", "value": "featuredPool=4;10796,featuredPool=4;10795,featuredPool=4;10794"}, {"desc": "高佣金率商品", "name": "高佣", "value": "featured=30日高佣好货,featured=7日高佣好货,featured=90日高佣好货"}, {"name": "秒杀引流", "value": "featured=秒杀品"}, {"name": "稀缺", "value": "featured=稀缺品牌"}, {"desc": "高销量或高成交商品", "name": "热销", "value": "featured=销量top单品,featured=成交top单品"}, {"desc": "实惠便宜的好货", "name": "超级好价", "value": "starLevel=4,starLevel=5,compareResult=-1,realTimeCompareResult=-1"}, {"name": "新品", "value": "newItemTag=重磅新品,newItemTag=优质新品,newItemTag=天猫新品,newItemTag=淘宝新品"}, {"desc": "淘宝搜索高曝光商品", "name": "高曝品", "value": "isHotItem=是"}], "title": "商品特色", "type": "button"}], "filterTitle": "商品特色"}, {"filterKey": "itemSource", "filterList": [{"key": "icTags", "multiple": true, "optionList": [{"name": "全部", "value": ""}, {"name": "直播严选", "value": "2156610,2205634"}, {"name": "天猫超市", "value": "2388418"}, {"name": "源头优选", "value": "2243138"}, {"name": "淘工厂", "value": "1362498"}, {"name": "天天热卖", "value": "2758466,3554370"}, {"name": "天猫U先", "value": "271938"}, {"name": "淘宝秒杀", "value": "1792066"}, {"name": "淘宝买菜", "value": "3117442"}], "title": "商品渠道", "type": "button"}], "filterTitle": "商品渠道"}], "errorCode": "0000", "info": "成功"}, "ret": ["SUCCESS::调用成功"], "traceId": "214781c217539469413952390e11a5", "v": "1.0"}