 // 数据处理器集合
    const dataProcessors = {
        // 全部商品渠道数据处理器
        processAllProductsData: function(data) {
            try {
                if (!data || !data.data || !data.data.data || !data.data.data.list) {
                    logDebug('[全部商品渠道] 数据结构异常', data);
                    return [];
                }

                const items = data.data.data.list;
                logDebug(`[全部商品渠道] 获取到 ${items.length} 条商品数据`);

                const parsedData = items.map(item => {
                    const extendVal = item.extendVal || {};
                    
                    // 调试：打印原始数据结构的关键部分
                    logDebug(`[数据结构调试] 商品${item.itemId}:`, {
                        hasChannelSlotInfo: !!(extendVal.channelSlotInfo),
                        channelSlotText: extendVal.channelSlotInfo?.channelSlotText,
                        hasSoldQuantity365: !!(extendVal.soldQuantity365),
                        soldQuantity365: extendVal.soldQuantity365,
                        hasSoldQuantityDisplay365: !!(extendVal.soldQuantityDisplay365),
                        soldQuantityDisplay365: extendVal.soldQuantityDisplay365,
                        itemSoldQuantity365: item.soldQuantity365,
                        placedOrdersIn30: extendVal.placedOrdersIn30
                    });
                    
                    // 提取渠道信息 - 从extendVal中获取
                    let channelSlotText = '';
                    if (extendVal.channelSlotInfo && extendVal.channelSlotInfo.channelSlotText) {
                        channelSlotText = extendVal.channelSlotInfo.channelSlotText;
                    }
                    
                    // 提取365天销量并转换为数字 - 首先尝试从extendVal获取，然后从item获取
                    let soldQuantity365 = '';
                    let soldQuantity365Number = 0;
                    
                    // 优先尝试多种365天销量数据源
                    if (extendVal.soldQuantity365) {
                        soldQuantity365 = extendVal.soldQuantity365;
                        soldQuantity365Number = convertQuantityToNumber(extendVal.soldQuantity365);
                    } else if (extendVal.soldQuantityDisplay365) {
                        // 处理 soldQuantityDisplay365 对象格式
                        soldQuantity365Number = convertQuantityToNumber(extendVal.soldQuantityDisplay365);
                        soldQuantity365 = extendVal.soldQuantityDisplay365.value + extendVal.soldQuantityDisplay365.unit;
                    } else if (item.soldQuantity365) {
                        soldQuantity365 = item.soldQuantity365;
                        soldQuantity365Number = convertQuantityToNumber(item.soldQuantity365);
                    }
                    
                    // 提取30天主播成交数量并转换为数字
                    let placedOrdersIn30 = extendVal.placedOrdersIn30 || '';
                    let placedOrdersIn30Number = 0;
                    if (placedOrdersIn30) {
                        placedOrdersIn30Number = convertPlacedOrdersToNumber(placedOrdersIn30);
                        logDebug(`[数据提取] 商品${item.itemId}: 30天主播成交 "${placedOrdersIn30}" -> ${placedOrdersIn30Number}`);
                    }
                    
                    // 提取销量增长率并转换为数字
                    let salesRiseCompareLastWeek = extendVal.sales_rise_compare_last_week || '';
                    let salesRiseCompareLastWeekNumber = 0;
                    if (salesRiseCompareLastWeek) {
                        salesRiseCompareLastWeekNumber = convertSalesRiseToNumber(salesRiseCompareLastWeek);
                        logDebug(`[数据提取] 商品${item.itemId}: 销量增长率 "${salesRiseCompareLastWeek}" -> ${salesRiseCompareLastWeekNumber}%`);
                    }
                    
                    // 提取店铺信息
                    let shopName = '';
                    let safeShopId = '';
                    if (extendVal.shopInfo) {
                        shopName = extendVal.shopInfo.shopName || '';
                        safeShopId = extendVal.shopInfo.safeShopId || '';
                        if (shopName || safeShopId) {
                            logDebug(`[数据提取] 商品${item.itemId}: 店铺名称 "${shopName}", 店铺ID "${safeShopId}"`);
                        }
                    }
                    
                    if (soldQuantity365) {
                        logDebug(`[数据提取] 商品${item.itemId}: 365天销量 "${soldQuantity365}" -> ${soldQuantity365Number}`);
                    }

                    if (channelSlotText) {
                        logDebug(`[数据提取] 商品${item.itemId}: 渠道 "${channelSlotText}"`);
                    }

                    return {
                        itemId: item.itemId || '',
                        itemName: item.itemName || '',
                        itemPrice: item.itemPrice || '',
                        commission: extendVal.commissionPrice || '',
                        soldQuantity30: extendVal.soldQuantity30 || item.soldQuantity || '',
                        soldQuantityLive7d: extendVal.soldQuantityLive7d || '',
                        soldQuantityLive1d: extendVal.soldQuantityLive1d || '',
                        soldQuantity365: soldQuantity365,
                        soldQuantity365Number: soldQuantity365Number,
                        channelSlotText: channelSlotText,
                        shopName: shopName,
                        safeShopId: safeShopId,
                        tcpCommission: extendVal.tcpCommission || '',
                        tcpCommissionType: extendVal.tcpCommissionType || '',
                        sales_rise_compare_last_week: salesRiseCompareLastWeek,
                        sales_rise_compare_last_week_number: salesRiseCompareLastWeekNumber,
                        placedOrdersIn30: placedOrdersIn30,
                        placedOrdersIn30Number: placedOrdersIn30Number,
                        featureTags: item.featureTags || []
                    };
                });

                logDebug(`[全部商品渠道] 成功提取 ${parsedData.length} 条商品数据`);
                return parsedData;
            } catch (e) {
                logDebug('[全部商品渠道] 解析失败', e);
                return [];
            }
        }
    };