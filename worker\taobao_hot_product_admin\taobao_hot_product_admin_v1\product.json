{"api": "mtop.taobao.cic.downlink.newstation.itemcenter.item.query", "data": {"data": {"currentPage": 1, "end": true, "list": [{"allSales30d": "0", "extendVal": {"soldQuantityDisplay30": {"unit": "", "value": "1"}, "hasDirectPlan": false, "itemCardFeaturedTags": [], "saleCntTrend": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "tcpCommission": "1%", "allSales30d": "20+", "icTags": [2049, 249858, 287745, 479233, 599041, 771073, 2059, 23563, 33803, 845825, 1622018, 1843202, 3137538, 2948098, 2989058, 3198978, 3246082, 3247106, 3424258, 3502082, 4059138, 49218, 137281, 361537, 363585, 404546, 4166, 498753, 432193, 535617, 567361, 11339, 32843, 33867, 866369, 849985, 1739842, 2021442, 2518082, 2589762, 2819138, 2668610, 2683970, 2962498, 2989122, 3315778, 3160130, 3215426, 3246146, 3572802, 3629122, 3923010, 4017218, 28802, 389249, 264321, 287873, 466049, 1163, 866433, 1517698, 2016386, 2058370, 2587778, 2668674, 2679938, 2683010, 2694274, 2988162, 3323010, 3166338, 3242114, 3443842, 3512450, 552129, 558273, 725185, 767169, 203, 7371, 23755, 848065, 1987778, 2032834, 2057410, 3315906, 3203266, 3218626, 3246274, 3413186, 3442882, 100609, 173313, 187649, 192769, 601345, 637185, 1291, 6411, 37131, 658689, 858369, 801025, 820481, 827649, 845057, 2036994, 2057474, 2522370, 2858242, 3143938, 3185922, 3198210, 3203330, 3218690, 3586306, 4064514, 4075778, 4008194, 4059394, 4061442, 241985, 107842, 348481, 350529, 323905, 7494, 15686, 622913, 532801, 18763, 5451, 27979, 37195, 704833, 845121, 2057538, 1835330, 2283842, 2193730, 2602306, 2762050, 2878786, 3016002, 3161410, 3196226, 3203394, 3218754, 3247426, 3668290, 3691842, 3974466, 4034882, 87425, 253313, 101762, 191873, 444802, 299394, 523649, 349570, 409985, 622977, 567681, 721281, 743809, 4491, 2443, 16779, 657793, 838017, 844161, 1727874, 2057602, 3323266, 3196290, 3203458, 3218818, 3246466, 3668354, 3851650, 3775874, 4077954, 3974530, 33217, 15810, 145857, 155073, 179649, 281025, 1478, 414145, 623041, 733633, 11723, 1483, 2507, 6603, 35275, 861633, 818625, 840129, 1518018, 2057666, 2283970, 2304450, 2340290, 3108290, 3315138, 3196354, 3218882, 3246530, 3668418, 3413442, 3747266, 3948994, 120321, 354817, 4614, 459265, 439809, 623105, 629249, 544257, 741889, 759297, 24075, 40459, 855553, 858625, 844289, 1518082, 2057730, 2409986, 2874882, 3061250, 2976258, 3338754, 3404290, 3196418, 3218946, 3246594, 3668482, 3444226, 36417, 258625, 141889, 360001, 4678, 591425, 735809, 2635, 1611, 7755, 34379, 2254402, 2317890, 2135618, 2203202, 3378754, 3196482, 3246658, 3619394, 3634754, 3668546, 3419714, 22145, 39553, 200321, 286337, 414337, 621185, 555649, 1675, 8843, 22155, 858753, 1624706, 2681474, 3381890, 3225218, 3246722, 3634818, 3668610, 25282, 347841, 353985, 417473, 4811, 7883, 867009, 1624770, 2057922, 2321090, 2128578, 2132674, 2493122, 2520770, 2602690, 3338946, 3246786, 3634882, 3668674, 3436226, 3777218, 4031170, 120577, 123649, 166658, 65281, 203521, 96002, 347905, 426754, 11015, 417537, 648961, 7947, 827137, 1437442, 1904386, 2493186, 2563842, 2729730, 3060482, 2943746, 3001090, 3337986, 3157762, 3246850, 3634946, 3668738, 3742466, 3974914, 141121, 350017, 365377, 16198, 3911, 11079, 628545, 4939, 11083, 676673, 1900354, 2256706, 2340674, 2493250, 2512706, 2902850, 2988866, 3245890, 3635010, 3445570, 376705, 265089, 3974, 7046, 11143, 649089, 716673, 718721, 866177, 822145, 829313, 841601, 1789826, 1998722, 2310018, 2493314, 2516866, 2388866, 2407298, 2933634, 2988930, 3185538, 3228546, 3245954, 3246978, 3533698, 67521, 245697, 260033, 334785, 376769, 3015, 624577, 654273, 18379, 19403, 37835, 38859, 39883, 857025, 845761, 2257858, 2119618, 3085250, 2988994, 3322818, 3246018, 3247042, 3469250], "allowMeasurement": true, "commissionPriceInteger": "0", "tcpPlanId": 73630394486, "notRecommendCheckStatus": true, "deliveryAddr": "河北沧州", "saleCntTrend7d": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "commissionPriceUnit": "", "shopInfo": {"bcType": "B", "bshop": true, "dingDing": "15075797270", "extMap": {"rankType": 3, "rankLevel": 3, "shopAges": 6, "rankImg": "//gtms01.alicdn.com/tps/i1/TB1yHn9HpXXXXaOXFXX3e.oIVXX-78-24.png", "shopFanNum": 86321}, "picUrl": "//img.alicdn.com/imgextra/7f/99/TB1BuGclET1gK0jSZFrSuwNCXXa.jpg", "safeShopId": "RAzN8BQm1uXApyUynvizsyCX2EpQS", "shopName": "逆龄旗舰店"}, "tcpCommissionEndTime": "长期有效", "behalfied": false, "newStationSoldQuantityAll30": "1", "soldQuantityLive7d": "0", "sellerInfo": {"sellerNick": "逆***"}, "cardFiledInfo": "3_5;2_95", "hasSellerPhone": true, "tcpPlanType": 2, "isChannelItemDownLink": false, "soldQuantityDisplay365": {"unit": "+", "value": "20"}, "anchorCntTrend7d": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "deliveryTime": "", "fuzzyQuantity": "200+", "finalPrice": "11.9", "peerAnchorIds": "[]", "itemCardTagAll": [], "soldQuantity365": "20+", "priceCompareInCate": "比95%同类商品便宜", "cardFiledInfoDisplay": "比95%同类商品便宜", "tcpCommissionType": "通用", "soldQuantityDisplay7": {"unit": "", "value": "0"}, "anchorCntLive7d": "", "pageSourceBackward": "^^ha3WareHouse", "anchorCntLive30d": "", "showYearSaleCnt": "true", "itemCardInfo": {"filedInfo": {"displayInfo": "比95%同类商品便宜"}, "filedInfoKey": "priceCompareInCate"}, "commissionPrice": "0.12", "itemCardTag": ["低价引流"], "soldQuantityLive1d": "0", "itemCardType": "2", "anchorCntTrend": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "soldQuantity30": "1", "isGoGoBuy": false, "itemYanxuan": false, "hasPhone": true, "commissionPriceFraction": ".12", "itemPrice": "11.9", "tcpCommissionNumber": 100, "tcpCommissionDeadline": "长期有效"}, "featureTags": ["低价引流"], "finalPrice": "11.9", "itemId": 673851026263, "itemName": "逆龄N33咬唇刷口红刷圆头晕染便携带盖一支装化妆师专用化妆刷", "itemPic": "//img.alicdn.com/imgextra/i4/2206727724486/O1CN01dgdjbD1j0dho5Vzp1_!!0-item_pic.jpg", "itemPrice": "11.9", "itemUrl": "//item.taobao.com/item.htm?id=673851026263", "showYearSaleCnt": "true", "soldQuantity": "1", "soldQuantity365": "20+", "status": 0}], "pageSize": 30, "total": 1, "totalPage": 1}, "errorCode": "0000", "info": "成功"}, "ret": ["SUCCESS::调用成功"], "traceId": "2150469c17539492964586211e12dc", "v": "1.0"}