{"name": "hot-taobao-job-admin-v3", "version": "1.0.0", "private": true, "type": "module", "scripts": {"start": "node src/server.js", "dev": "node --watch src/server.js", "init-db": "node src/init-db.js", "job": "node src/run-job.js"}, "dependencies": {"express": "^4.18.2", "better-sqlite3": "^9.2.2", "node-cron": "^3.0.3", "dotenv": "^16.3.1", "cors": "^2.8.5", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.0.2"}}