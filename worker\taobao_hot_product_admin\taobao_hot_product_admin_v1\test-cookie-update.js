/**
 * <PERSON>ie更新功能测试脚本
 */

import { database } from './src/database.js';
import { batchUpdateAllAnchorsCookies } from './src/cookie-update-service.js';
import cookieScheduler from './src/cookie-scheduler.js';

async function testCookieUpdate() {
    console.log('=== Cookie更新功能测试 ===\n');

    try {
        // 1. 测试数据库连接
        console.log('1. 测试数据库连接...');
        const anchorsResult = await database.all(
            "SELECT id, anchor_name, anchor_id, status FROM anchors LIMIT 3"
        );
        
        if (anchorsResult.results && anchorsResult.results.length > 0) {
            console.log(`✅ 数据库连接正常，找到 ${anchorsResult.results.length} 个主播`);
            anchorsResult.results.forEach(anchor => {
                console.log(`   - ${anchor.anchor_name} (${anchor.anchor_id}) - ${anchor.status}`);
            });
        } else {
            console.log('⚠️ 数据库中没有主播数据');
        }
        console.log('');

        // 2. 测试Cookie更新服务
        console.log('2. 测试Cookie更新服务...');
        console.log('注意：这将调用真实的淘宝API，请确保主播Cookie有效');
        console.log('如果不想执行真实更新，请按 Ctrl+C 退出');
        
        // 等待3秒让用户有机会取消
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const updateResult = await batchUpdateAllAnchorsCookies();
        
        if (updateResult.success) {
            console.log('✅ Cookie更新测试成功');
            console.log(`   总主播数: ${updateResult.totalAnchors}`);
            console.log(`   已更新: ${updateResult.updatedCount}`);
            console.log(`   无需更新: ${updateResult.noUpdateCount}`);
            console.log(`   失败: ${updateResult.failedCount}`);
        } else {
            console.log('❌ Cookie更新测试失败:', updateResult.error);
        }
        console.log('');

        // 3. 测试定时任务调度器
        console.log('3. 测试定时任务调度器...');
        
        const initialStatus = cookieScheduler.getStatus();
        console.log(`   初始状态: ${initialStatus.isRunning ? '运行中' : '未运行'}`);
        
        if (!initialStatus.isRunning) {
            console.log('   启动定时任务...');
            cookieScheduler.start();
            
            const startedStatus = cookieScheduler.getStatus();
            if (startedStatus.isRunning) {
                console.log('✅ 定时任务启动成功');
            } else {
                console.log('❌ 定时任务启动失败');
            }
            
            // 停止定时任务
            console.log('   停止定时任务...');
            cookieScheduler.stop();
            
            const stoppedStatus = cookieScheduler.getStatus();
            if (!stoppedStatus.isRunning) {
                console.log('✅ 定时任务停止成功');
            } else {
                console.log('❌ 定时任务停止失败');
            }
        } else {
            console.log('✅ 定时任务已在运行中');
        }
        console.log('');

        // 4. 显示测试总结
        console.log('=== 测试总结 ===');
        console.log('✅ 所有核心功能测试完成');
        console.log('');
        console.log('功能说明：');
        console.log('- 手动更新：在主播管理页面点击"更新Cookie"按钮');
        console.log('- 自动更新：系统启动后每2分钟自动执行');
        console.log('- 状态显示：页面每30秒刷新一次统计信息');
        console.log('');
        console.log('启动服务器命令：npm start');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        console.error('错误详情:', error.stack);
    }
}

// 运行测试
testCookieUpdate().then(() => {
    console.log('\n测试完成，退出程序');
    process.exit(0);
}).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
});
