<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选品规则配置 - 淘宝热销产品管理系统</title>

    <script src="https://registry.npmmirror.com/tailwindcss-cdn/3.4.10/files/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            margin: 20px;
            overflow: hidden;
        }

        .nav-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-bottom: 1px solid #e2e8f0;
            padding: 0 2rem;
        }

        .page-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .content-grid {
            display: grid;
            grid-template-columns: 350px 1fr 400px;
            gap: 1.5rem;
            padding: 2rem;
            min-height: 600px;
        }

        @media (max-width: 1600px) {
            .content-grid {
                grid-template-columns: 300px 1fr 350px;
            }
        }

        @media (max-width: 1400px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        /* 新增综合配置样式 */
        .config-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid #e2e8f0;
            margin-bottom: 1rem;
        }

        .position-chart {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .position-bar {
            height: 24px;
            background: #e2e8f0;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .position-segment {
            height: 100%;
            position: absolute;
            top: 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 500;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .group-summary-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .group-summary-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .conflict-warning {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
        }

        .conflict-warning i {
            color: #ef4444;
        }

        /* 预览区域样式 */
        .preview-analysis-section {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            margin: 0 2rem 2rem;
            overflow: hidden;
        }

        .preview-content {
            padding: 1.5rem;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .analysis-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .analysis-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .analysis-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .analysis-change {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .analysis-change.positive {
            color: var(--success-color);
        }

        .analysis-change.negative {
            color: var(--danger-color);
        }

        .group-distribution {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .distribution-item {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .distribution-item:nth-child(2) { background: #10b981; }
        .distribution-item:nth-child(3) { background: #f59e0b; }
        .distribution-item:nth-child(4) { background: #ef4444; }
        .distribution-item:nth-child(5) { background: #8b5cf6; }
        .distribution-item:nth-child(6) { background: #ec4899; }

        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .rule-group-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .rule-group-item:hover {
            border-color: var(--primary-color);
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .rule-group-item.active {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            transform: translateX(4px);
        }

        .rule-group-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(107, 114, 128, 0.3);
        }

        .btn-outline {
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            background-color: transparent;
        }

        .btn-outline:hover {
            color: white;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .btn-outline:focus {
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .btn-outline:active {
            color: white;
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.compact {
            margin-bottom: 0.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--gray-200);
            border-radius: 8px;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .condition-builder {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        .condition-row {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .condition-row:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .condition-row:last-child {
            margin-bottom: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-600);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .preview-area {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
        }

        .preview-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .preview-item:hover {
            border-color: var(--primary-color);
            transform: translateX(4px);
        }

        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--gray-300);
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            transition: width 0.3s ease;
        }

        .tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: var(--gray-100);
            color: var(--gray-700);
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .tag.active {
            background: var(--primary-color);
            color: white;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .table-container {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 1px solid #e2e8f0;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            color: var(--gray-700);
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--gray-600);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: #f3f4f6;
            color: var(--primary-color);
        }

        .header-actions {
            display: flex;
            gap: 0.75rem;
        }

        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
        }

        .rule-list-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rule-list-item:hover {
            border-color: var(--primary-color);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .rule-list-item.active {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        }

        .rule-info {
            flex: 1;
        }

        .rule-title {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }

        .rule-meta {
            font-size: 0.75rem;
            color: var(--gray-500);
            display: flex;
            gap: 1rem;
        }

        .rule-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.375rem;
        }

        .preview-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .preview-list-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .preview-list-item:hover {
            border-color: var(--primary-color);
            transform: translateX(2px);
        }

        .preview-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .preview-item-title {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .preview-item-score {
            background: var(--primary-color);
            color: white;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .preview-item-details {
            font-size: 0.75rem;
            color: var(--gray-600);
            display: flex;
            gap: 1rem;
        }

        .config-form {
            max-width: 600px;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .preview-table-container {
            height: calc(100vh - 200px);
            overflow-y: auto;
            overflow-x: hidden;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
        }

        .table-wrapper {
            height: 100%;
            overflow-y: auto;
            border: none;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 0.875rem;
        }

        .preview-table th {
            background: #f8fafc;
            padding: 0.75rem 0.5rem;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            font-size: 0.8rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .preview-table td {
            padding: 0.75rem 0.5rem;
            border-bottom: 1px solid #f3f4f6;
            border-right: 1px solid #f3f4f6;
            color: var(--gray-700);
            font-size: 0.8rem;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .preview-table tbody tr:hover {
            background: #f8fafc;
        }

        .preview-table tbody tr:nth-child(even) {
            background: #fafbfc;
        }

        .preview-table tbody tr:nth-child(even):hover {
            background: #f3f4f6;
        }

        .score-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-block;
            min-width: 30px;
            text-align: center;
        }

        .product-name {
            font-weight: 500;
            color: var(--gray-900);
            max-width: 300px;
        }

        .price {
            color: #dc2626;
            font-weight: 600;
        }

        .rating {
            color: #059669;
            font-weight: 500;
        }

        .sales {
            color: var(--gray-600);
        }

        /* 产品列表滚动条样式 */
        .table-wrapper::-webkit-scrollbar {
            width: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 产品列表容器 */
        .product-list-container {
            height: 450px;
            overflow: hidden;
            background: white;
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- 导航栏 -->
        <nav class="nav-container">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">
                        <i class="fas fa-cogs text-blue-500 mr-2"></i>
                        淘宝热销产品管理系统
                    </h1>
                </div>
                <div class="nav-links">
                    <a href="products.html" class="nav-link">
                        <i class="fas fa-box"></i>产品管理
                    </a>
                    <a href="selection-rules.html" class="nav-link">
                        <i class="fas fa-filter"></i>选品规则
                    </a>
                    <a href="rule-management.html" class="nav-link">
                        <i class="fas fa-cogs"></i>规则管理
                    </a>
                    <a href="anchors.html" class="nav-link">
                        <i class="fas fa-microphone"></i>主播管理
                    </a>
                    <a href="product-collection.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>商品采集
                    </a>
                </div>
            </div>
        </nav>

        <!-- 页面标题区域 -->
        <div class="page-header">
            <div class="relative z-10">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">选品规则配置</h2>
                        <p class="text-blue-100 text-lg">智能配置产品选择规则，支持多条件组合筛选</p>
                    </div>
                    <div class="header-actions">
                        <button id="newRuleBtn" class="btn btn-outline">
                            <i class="fas fa-plus"></i>新建规则
                        </button>
                        <button id="saveRuleBtn" class="btn btn-primary">
                            <i class="fas fa-save"></i>保存规则
                        </button>
                        <button id="executeRuleBtn" class="btn btn-success">
                            <i class="fas fa-play"></i>执行选品
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选模块 -->
        <div style="padding: 2rem 2rem 0;">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-filter text-blue-500"></i>
                        数据筛选
                    </h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="form-group">
                            <label class="form-label">规则列表</label>
                            <select id="ruleFilter" class="form-control">
                                <option value="">请选择规则</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">主播列表</label>
                            <select id="anchorFilter" class="form-control">
                                <option value="">请选择主播</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">选择日期</label>
                            <input type="date" id="dateFilter" class="form-control">
                        </div>
                        <div class="form-group flex items-end">
                            <button id="loadRuleBtn" class="btn btn-primary w-full">
                                <i class="fas fa-download mr-2"></i>加载规则
                            </button>
                        </div>
                    </div>
                    <div id="filterWarning" class="hidden mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                            <span class="text-yellow-700">请选择规则、主播和日期后再加载规则</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则基本信息 -->
        <div style="padding: 2rem;">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle text-blue-500"></i>
                        规则基本信息
                        <span class="text-sm text-gray-500 font-normal ml-2">（可直接编辑）</span>
                    </h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label class="form-label">规则名称</label>
                            <input type="text" id="ruleName" class="form-control" placeholder="请输入规则名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">总目标数量</label>
                            <input type="number" id="totalTarget" value="500" class="form-control">
                        </div>
                        <div class="form-group">
                            <label class="form-label">规则描述</label>
                            <input type="text" id="ruleDescription" class="form-control" placeholder="请输入规则描述">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-grid">
            <!-- 左侧：规则组管理 -->
            <div class="card">
                <div class="card-header">
                    <div class="flex justify-between items-center">
                        <h3 class="card-title">
                            <i class="fas fa-layer-group text-purple-500"></i>
                            规则组管理
                        </h3>
                        <button id="addGroupBtn" class="btn btn-primary" style="padding: 0.5rem 1rem; font-size: 0.75rem;">
                            <i class="fas fa-plus"></i>添加组
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="ruleGroupsList" class="mb-4">
                        <!-- 规则组将在这里动态生成 -->
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="totalTargetDisplay">500</div>
                            <div class="stat-label">目标总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="currentConfigTotal">0</div>
                            <div class="stat-label">配置总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="estimatedMatch">--</div>
                            <div class="stat-label">预计匹配</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="completionRate">0%</div>
                            <div class="stat-label">完成率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间：规则配置区域 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs text-green-500"></i>
                        规则配置
                    </h3>
                </div>
                <div class="card-body">
                    <div id="ruleConfigArea">
                        <div class="empty-state">
                            <i class="fas fa-mouse-pointer"></i>
                            <h3 class="text-lg font-semibold text-gray-700 mb-2">开始配置规则</h3>
                            <p class="text-gray-500">请选择左侧的规则组进行配置</p>
                            <p class="text-sm text-gray-400 mt-2">或点击"添加组"创建新的规则组</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：综合配置 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sliders-h text-blue-500"></i>
                        综合配置
                    </h3>
                </div>
                <div class="card-body">
                    <div id="comprehensiveConfigArea">
                        <div class="empty-state">
                            <i class="fas fa-puzzle-piece"></i>
                            <h3 class="text-lg font-semibold text-gray-700 mb-2">综合配置</h3>
                            <p class="text-gray-500">添加规则组后显示配置选项</p>
                            <p class="text-sm text-gray-400 mt-2">管理产品位置、去重和优先级</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品预览区域 -->
        <div class="preview-analysis-section">
            <div class="preview-content">
                <div class="analysis-grid">
                    <div class="analysis-card">
                        <div class="analysis-value" id="previewCount">0</div>
                        <div class="analysis-label">匹配产品</div>

                    </div>
                    <div class="analysis-card">
                        <div class="analysis-value" id="previewScore">0%</div>
                        <div class="analysis-label">匹配度</div>

                    </div>
                    <div class="analysis-card">
                        <div class="analysis-value" id="avgPrice">¥0</div>
                        <div class="analysis-label">平均价格</div>

                    </div>
                    <div class="analysis-card">
                        <div class="analysis-value" id="avgCommission">¥0</div>
                        <div class="analysis-label">平均佣金</div>

                    </div>
                </div>

                <!-- 冲突提示 -->
                <div id="conflictInfo" class="hidden bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <!-- 冲突信息将在这里显示 -->
                </div>

                <!-- 组统计 -->
                <div id="groupStats" class="mb-4">
                    <!-- 组统计信息将在这里显示 -->
                </div>

                <!-- 产品预览表格 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="flex justify-between items-center p-4 border-b border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900">产品列表</h4>
                        <div class="flex items-center space-x-2">
                            <span id="recordCount" class="text-sm text-gray-500">共 0 条记录</span>
                            <button id="copyPreviewBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded text-sm transition-colors" style="display: none;">
                                <i class="fas fa-copy mr-1"></i>复制结果
                            </button>
                        </div>
                    </div>
                    <div class="product-list-container">
                        <div class="table-wrapper">
                            <table class="preview-table">
                                <thead>
                                    <tr>
                                        <th>位置</th>
                                        <th>产品ID</th>
                                        <th>产品名称</th>
                                        <th>价格</th>
                                        <th>佣金率</th>
                                        <th>佣金金额</th>
                                        <th>产品来源</th>
                                        <th>联盟渠道</th>
                                        <th>30天销量</th>
                                        <th>7天增长率</th>
                                        <th>标签</th>
                                        <th>所属组</th>
                                        <th>优先级</th>
                                        <th>主播</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody id="previewTableBody">
                                    <tr>
                                        <td colspan="15" class="text-center text-gray-500 py-8">
                                            <i class="fas fa-search text-2xl mb-2 block"></i>
                                            配置规则后显示匹配结果
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 执行历史 -->
        <div style="padding: 0 2rem 2rem;">
            <div class="table-container">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history text-indigo-500"></i>
                        执行历史
                    </h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>执行时间</th>
                                <th>规则名称</th>
                                <th>选中数量</th>
                                <th>批次号</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="executionHistoryTable">
                            <!-- 执行历史将在这里动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动操作按钮 -->
    <button class="floating-action" id="quickAddBtn" title="快速添加规则组">
        <i class="fas fa-plus"></i>
    </button>

    <!-- 保存规则模态框 -->
    <div id="saveRuleModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="text-lg font-semibold text-gray-900">保存选品规则</h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">规则名称</label>
                    <input type="text" id="saveRuleName" class="form-control" placeholder="请输入规则名称">
                </div>
                <div class="form-group">
                    <label class="form-label">规则描述</label>
                    <textarea id="saveRuleDescription" rows="3" class="form-control" placeholder="请输入规则描述"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancelSaveBtn" class="btn btn-outline">取消</button>
                <button id="confirmSaveBtn" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>



    <script src="js/selection-rules.js"></script>
    <script>
        // 标签名称映射函数
        function getTagName(tag) {
            const tagNames = {
                1: '大类目',
                2: '流量品', 
                3: '店铺品',
                4: '出单同类品'
            };
            return tagNames[tag] || '未知';
        }
    </script>
</body>

</html>
