import { parseSetCookieFromResponse, updateAnchorCookie } from './cookie-utils.js';
import { database } from '../database.js';

/**
 * 处理淘宝API响应，仅解析Cookie不更新数据库
 * @param {Response} response
 * @param {string} anchorName
 * @param {string} currentCookie
 * @returns {Promise<object>}
 */
export async function handleTaobaoApiResponse(response, anchorName, currentCookie) {
    try {
        // 检查响应中是否有新的Cookie
        const cookieInfo = parseSetCookieFromResponse(response);

        if (cookieInfo.hasNewCookies) {
            console.log(`[${anchorName}] 检测到 ${Object.keys(cookieInfo.cookies).length} 个新Cookie`);

            // 更新Cookie字符串但不保存到数据库
            const updatedCookie = updateAnchorCookie(anchorName, cookieInfo.cookies, currentCookie);

            return {
                cookieUpdated: true,
                newCookie: updatedCookie,
                cookies: cookieInfo.cookies
            };
        } else {
            console.log(`[${anchorName}] 响应中无新Cookie`);
            return {
                cookieUpdated: false,
                message: '无新Cookie'
            };
        }
    } catch (error) {
        console.error(`[${anchorName}] 响应处理失败:`, error);
        return {
            cookieUpdated: false,
            error: error.message
        };
    }
}
