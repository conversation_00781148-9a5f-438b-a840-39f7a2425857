import cron from 'node-cron';
import { batchUpdateAllAnchorsCookies } from './cookie-update-service.js';

/**
 * Cookie定时更新调度器
 */
class CookieScheduler {
    constructor() {
        this.isRunning = false;
        this.task = null;
        this.lastUpdateTime = null;
        this.updateStats = {
            totalRuns: 0,
            successRuns: 0,
            failedRuns: 0,
            lastResult: null
        };
    }

    /**
     * 启动定时任务 - 每10分钟执行一次
     */
    start() {
        if (this.isRunning) {
            console.log('⚠️ Cookie定时更新任务已在运行中');
            return;
        }

        console.log('🚀 启动Cookie定时更新任务 - 每10分钟执行一次');

        // 每10分钟执行一次：'*/10 * * * *'
        this.task = cron.schedule('*/10 * * * *', async () => {
            await this.executeUpdate();
        }, {
            scheduled: false,
            timezone: "Asia/Shanghai"
        });

        this.task.start();
        this.isRunning = true;
        
        console.log('✅ Cookie定时更新任务已启动');
    }

    /**
     * 停止定时任务
     */
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ Cookie定时更新任务未在运行');
            return;
        }

        if (this.task) {
            this.task.stop();
            this.task = null;
        }

        this.isRunning = false;
        console.log('🛑 Cookie定时更新任务已停止');
    }

    /**
     * 执行Cookie更新
     */
    async executeUpdate() {
        const startTime = new Date();
        console.log(`⏰ [${startTime.toLocaleString()}] 开始定时Cookie更新...`);
        
        this.updateStats.totalRuns++;

        try {
            const result = await batchUpdateAllAnchorsCookies();
            
            if (result.success) {
                this.updateStats.successRuns++;
                this.updateStats.lastResult = result;
                this.lastUpdateTime = startTime;
                
                console.log(`✅ [${startTime.toLocaleString()}] 定时Cookie更新完成: 总计${result.totalAnchors}个, 更新${result.updatedCount}个, 无需更新${result.noUpdateCount}个, 失败${result.failedCount}个`);
            } else {
                this.updateStats.failedRuns++;
                console.error(`❌ [${startTime.toLocaleString()}] 定时Cookie更新失败: ${result.error}`);
            }
        } catch (error) {
            this.updateStats.failedRuns++;
            console.error(`💥 [${startTime.toLocaleString()}] 定时Cookie更新异常:`, error);
        }

        const endTime = new Date();
        const duration = endTime - startTime;
        console.log(`⏱️ [${startTime.toLocaleString()}] Cookie更新耗时: ${duration}ms`);
    }

    /**
     * 获取任务状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastUpdateTime: this.lastUpdateTime,
            stats: this.updateStats
        };
    }

    /**
     * 手动执行一次更新（不影响定时任务）
     */
    async manualUpdate() {
        console.log('🔧 手动执行Cookie更新...');
        await this.executeUpdate();
    }
}

// 创建全局实例
const cookieScheduler = new CookieScheduler();

export default cookieScheduler;
