# 商品采集保存功能说明

## 功能概述

已为 `ProductCollection` 类添加了将采集到的商品数据保存到 `products` 表的功能。新增的方法可以自动处理数据格式转换、时区设置，并支持批量保存。

## 新增方法

### 1. `saveProductsToDatabase(anchorName, products)`

将商品数据保存到数据库的核心方法。

**参数:**
- `anchorName` (string): 主播名称
- `products` (array): 处理后的商品数据数组

**返回值:**
```javascript
{
    success: boolean,
    saved: number,        // 成功保存的商品数量
    total: number,        // 总商品数量
    errors: array|null    // 错误信息（如果有）
}
```

### 2. `collectAndSaveProducts(anchorId, filters, pageNum, pageSize, saveToDb)`

采集并保存商品数据的综合方法。

**参数:**
- `anchorId` (string): 主播ID
- `filters` (object): 过滤条件，默认 `{}`
- `pageNum` (number): 页码，默认 `1`
- `pageSize` (number): 每页数量，默认 `30`
- `saveToDb` (boolean): 是否保存到数据库，默认 `true`

### 3. `collectMultiplePagesAndSave(anchorId, filters, maxPages, pageSize)`

批量采集多页商品数据并保存。

**参数:**
- `anchorId` (string): 主播ID
- `filters` (object): 过滤条件，默认 `{}`
- `maxPages` (number): 最大页数，默认 `5`
- `pageSize` (number): 每页数量，默认 `30`

## 数据库字段映射

采集的商品数据会按照以下规则映射到 `products` 表：

| 数据库字段 | 数据来源 | 说明 |
|-----------|---------|------|
| anchor_name | 主播名称 | 从主播信息获取 |
| product_id | itemId | 商品ID |
| product_title | itemName | 商品标题 |
| product_price | itemPrice | 商品价格（处理后的数字） |
| commission_rate | tcpCommission | 佣金率（从佣金信息中提取） |
| commission_amount | 计算得出 | 佣金金额 = 价格 × 佣金率 / 100 |
| product_source | 固定值 | "淘宝" |
| alliance_channel | channelSlotText | 联盟渠道 |
| sales_365_days | soldQuantity365Number | 365天销量 |
| sales_30_days | soldQuantity30 | 30天销量 |
| sales_7_days | soldQuantityLive7d | 7天销量 |
| sales_7_days_growth_rate | sales_rise_compare_last_week_number | 7天销量增长率 |
| orders_30_days | placedOrdersIn30Number | 30天订单数 |
| shop_name | shopName | 店铺名称 |
| safe_shop_id | safeShopId | 安全店铺ID |
| feature_tags | featureTags | 特征标签（数组转字符串） |
| date | 当前日期 | UTC+8时区的年月日 |
| batch_number | 空字符串 | 默认为空 |
| created_at | 当前时间 | UTC+8时区的完整时间 |
| updated_at | 当前时间 | UTC+8时区的完整时间 |

## 使用示例

### 基本使用

```javascript
import ProductCollection from './product-collection.js';

const collector = new ProductCollection();

// 采集并保存单页商品数据
const result = await collector.collectAndSaveProducts(
    'anchor_id_123',  // 主播ID
    { code: "1001" }, // 过滤条件
    1,                // 页码
    30,               // 每页数量
    true              // 保存到数据库
);

if (result.success) {
    console.log(`采集成功: ${result.data.length} 个商品`);
    console.log(`保存成功: ${result.saveResult.saved} 个商品`);
}
```

### 批量采集多页

```javascript
// 批量采集前3页数据
const result = await collector.collectMultiplePagesAndSave(
    'anchor_id_123',  // 主播ID
    { code: "1001" }, // 过滤条件
    3,                // 最大页数
    30                // 每页数量
);

if (result.success) {
    console.log(`总共采集: ${result.totalProducts} 个商品`);
    console.log(`保存成功: ${result.totalSaved} 个商品`);
}
```

### 预览模式（不保存）

```javascript
// 只采集不保存，用于预览
const result = await collector.collectAndSaveProducts(
    'anchor_id_123',
    { code: "1001" },
    1,
    10,
    false  // 不保存到数据库
);
```

## 注意事项

1. **时区处理**: 所有时间字段都使用 UTC+8 时区
2. **事务处理**: 使用数据库事务确保数据一致性
3. **错误处理**: 单个商品保存失败不会影响其他商品
4. **数据验证**: 自动处理数据类型转换和空值
5. **请求频率**: 批量采集时会自动添加延迟避免请求过快
6. **重复数据**: 目前没有去重机制，可能会插入重复数据

## 错误处理

所有方法都会返回包含 `success` 字段的结果对象：

- `success: true` - 操作成功
- `success: false` - 操作失败，`error` 字段包含错误信息

对于批量操作，还会提供详细的统计信息和错误列表。
