# 商品采集去重功能测试

## 修复的问题

### 1. 产品ID格式化问题
**问题**: 产品ID会出现 `.0` 后缀
**解决**: 使用 `String(product.itemId).replace(/\.0$/, '')` 格式化为纯文本

### 2. 重复保存问题
**问题**: 相同的产品会重复保存到数据库
**解决**: 在保存前进行高效去重

## 去重逻辑

### 去重条件
根据以下字段组合进行去重：
- `anchor_name` (主播名称)
- `date` (日期)
- `product_id` (产品ID)
- `tag` (商品标签)

### 去重流程
1. **查询已存在的产品ID**: 查询当天该主播该标签已保存的所有产品ID
2. **内存去重**: 使用 `Set` 数据结构进行高效查找
3. **过滤重复**: 只保存不存在的产品
4. **统计信息**: 返回保存数量、跳过数量等详细信息

## 代码实现

### 核心去重方法
```javascript
async getExistingProductIds(anchorName, date, tag = null) {
    let query, params;

    if (tag !== null) {
        // 包含tag字段的查询
        query = "SELECT DISTINCT product_id FROM products WHERE anchor_name = ? AND date = ? AND tag = ?";
        params = [anchorName, date, tag];
    } else {
        // tag为null的查询
        query = "SELECT DISTINCT product_id FROM products WHERE anchor_name = ? AND date = ? AND tag IS NULL";
        params = [anchorName, date];
    }

    const result = await database.all(query, params);

    const productIds = new Set();
    if (result.results) {
        result.results.forEach(row => {
            if (row.product_id) {
                productIds.add(String(row.product_id));
            }
        });
    }

    return productIds;
}
```

### 去重过滤
```javascript
// 获取已存在的产品ID（包含tag字段）
const existingProductIds = await this.getExistingProductIds(anchorName, currentDate, tag);

// 过滤重复产品
const uniqueProducts = products.filter(product => {
    const productId = product.itemId ? String(product.itemId).replace(/\.0$/, '') : '';
    return productId && !existingProductIds.has(productId);
});
```

## 性能优化

### 1. 单次查询
- 一次性查询当天该主播的所有产品ID
- 避免在循环中进行数据库查询

### 2. Set数据结构
- 使用 `Set` 进行 O(1) 时间复杂度的查找
- 比数组查找效率高很多

### 3. 早期返回
- 如果所有产品都重复，直接返回，不执行数据库事务

## 返回结果

保存结果现在包含更详细的信息：

```javascript
{
    success: true,
    saved: 15,        // 实际保存的数量
    total: 30,        // 总商品数量
    skipped: 15,      // 跳过的重复商品数量
    errors: null      // 错误信息（如果有）
}
```

## 日志输出

控制台会显示详细的去重信息：
```
[主播名称] 原始商品数: 30, 去重后: 15, 跳过重复: 15
[主播名称] 成功保存 15 个商品到数据库
```

## 测试场景

### 场景1: 首次采集
- 输入: 30个新商品
- 预期: 保存30个，跳过0个

### 场景2: 重复采集
- 输入: 30个商品（全部已存在）
- 预期: 保存0个，跳过30个

### 场景3: 部分重复
- 输入: 30个商品（15个新，15个重复）
- 预期: 保存15个，跳过15个

### 场景4: 产品ID格式化
- 输入: 产品ID为 `123456789.0`
- 预期: 保存为 `123456789`（无.0后缀）

## 注意事项

1. **日期精度**: 去重基于日期（年月日），同一天内不会重复
2. **主播隔离**: 不同主播的相同产品ID不会冲突
3. **错误处理**: 查询失败时返回空Set，不影响保存流程
4. **事务安全**: 使用数据库事务确保数据一致性
