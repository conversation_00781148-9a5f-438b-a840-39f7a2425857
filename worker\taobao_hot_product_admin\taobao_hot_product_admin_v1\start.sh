#!/bin/bash

# 全局配置
APP_NAME="product"
START_COMMAND="pm2 start npm --name \"$APP_NAME\" -- start"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示标题
show_title() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}     PM2 应用管理工具${NC}"
    echo -e "${BLUE}     应用名称: ${GREEN}$APP_NAME${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

# 显示菜单
show_menu() {
    echo -e "${YELLOW}请选择操作:${NC}"
    echo "1. 启动应用"
    echo "2. 停止应用"
    echo "3. 重启应用"
    echo "4. 查看状态"
    echo "5. 查看日志"
    echo "6. 查看所有PM2进程"
    echo "7. 删除应用"
    echo "0. 退出"
    echo
}

# 启动应用
start_app() {
    echo -e "${BLUE}正在启动应用 $APP_NAME...${NC}"
    
    # 检查应用是否已经在运行
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        echo -e "${YELLOW}应用 $APP_NAME 已经存在，正在重启...${NC}"
        pm2 restart "$APP_NAME"
    else
        echo -e "${GREEN}正在启动新应用...${NC}"
        eval $START_COMMAND
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 应用启动成功!${NC}"
    else
        echo -e "${RED}✗ 应用启动失败!${NC}"
    fi
}

# 停止应用
stop_app() {
    echo -e "${BLUE}正在停止应用 $APP_NAME...${NC}"
    pm2 stop "$APP_NAME"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 应用停止成功!${NC}"
    else
        echo -e "${RED}✗ 应用停止失败或应用不存在!${NC}"
    fi
}

# 重启应用
restart_app() {
    echo -e "${BLUE}正在重启应用 $APP_NAME...${NC}"
    pm2 restart "$APP_NAME"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 应用重启成功!${NC}"
    else
        echo -e "${RED}✗ 应用重启失败或应用不存在!${NC}"
    fi
}

# 查看状态
show_status() {
    echo -e "${BLUE}应用 $APP_NAME 的状态:${NC}"
    pm2 describe "$APP_NAME"
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看应用 $APP_NAME 的日志 (按 Ctrl+C 退出):${NC}"
    echo -e "${YELLOW}最近50行日志:${NC}"
    pm2 logs "$APP_NAME" --lines 50
}

# 查看所有PM2进程
show_all_processes() {
    echo -e "${BLUE}所有PM2进程:${NC}"
    pm2 list
}

# 删除应用
delete_app() {
    echo -e "${RED}确定要删除应用 $APP_NAME 吗? (y/N)${NC}"
    read -r confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}正在删除应用 $APP_NAME...${NC}"
        pm2 delete "$APP_NAME"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ 应用删除成功!${NC}"
        else
            echo -e "${RED}✗ 应用删除失败或应用不存在!${NC}"
        fi
    else
        echo -e "${YELLOW}取消删除操作${NC}"
    fi
}

# 主循环
main() {
    while true; do
        clear
        show_title
        show_menu
        
        echo -n "请输入选项 [0-7]: "
        read -r choice
        
        case $choice in
            1)
                start_app
                ;;
            2)
                stop_app
                ;;
            3)
                restart_app
                ;;
            4)
                show_status
                ;;
            5)
                show_logs
                ;;
            6)
                show_all_processes
                ;;
            7)
                delete_app
                ;;
            0)
                echo -e "${GREEN}退出程序${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效的选项，请重新选择!${NC}"
                ;;
        esac
        
        echo
        echo -e "${YELLOW}按回车键继续...${NC}"
        read -r
    done
}

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo -e "${RED}错误: PM2 未安装!${NC}"
    echo -e "${YELLOW}请先运行: npm install -g pm2${NC}"
    exit 1
fi

# 运行主程序
main